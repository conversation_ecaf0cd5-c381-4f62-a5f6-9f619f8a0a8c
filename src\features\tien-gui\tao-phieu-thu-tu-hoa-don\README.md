1. Filter Data (<PERSON><PERSON><PERSON> h<PERSON><PERSON> đơn/phiếu thu):

```
POST /entities/{entity_slug}/erp/tao-phieu-thu-tu-hoa-don/filter/

// Request Body:
{
  "xu_ly": "1" | "2",  // "1": <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> đơn chưa thanh toán, "2": <PERSON><PERSON><PERSON> <PERSON>hiếu thu đã tạo
  "ngay_ct1": "2024-01-01",  // Required - Từ ngày
  "ngay_ct2": "2024-01-31",  // Required - <PERSON><PERSON>n ngày
  "so_ct1": "HD001",         // Optional - Từ số chứng từ
  "so_ct2": "HD999",         // Optional - Đến số chứng từ
  "ma_ct": "uuid",           // Optional - UUID loại chứng từ
  "ma_kh": "uuid",           // Optional - UUID khách hàng
  "ma_nt": "uuid",           // Optional - UUID ngoại tệ
  "ma_nk": "uuid",           // Optional - UUID quyển chứng từ
  "unit_id": "uuid"          // Optional - UUID đơn vị
}
```

2. Create Receipts (Tạo phiếu thu):

```
POST /entities/{entity_slug}/erp/tao-phieu-thu-tu-hoa-don/

// Request Body:
{
  "invoice_ids": ["uuid1", "uuid2", "uuid3"],  // Array UUID hóa đơn đã chọn
  "common_receipt_data": {
    "ma_ngv": "1",           // Mã nghiệp vụ
    "tk": "111",             // Tài khoản
    "ma_nt": "uuid",         // UUID ngoại tệ
    "ty_gia": 1,             // Tỷ giá
    "ngay_ct": "2024-01-15", // Ngày chứng từ
    "dien_giai": "Thu tiền bán hàng", // Diễn giải
    // ... các field khác
  }
}
```

3. Delete Receipt (Xóa phiếu thu):

```
POST /entities/{entity_slug}/erp/tao-phieu-thu-tu-hoa-don/delete-receipt/

// Request Body:
{
  "phieu_thu_uuid": "uuid"  // UUID phiếu thu cần xóa
}
```

4. Get Quyen Options (Lấy quyển chứng từ):

```
GET /entities/{entity_slug}/erp/tao-phieu-thu-tu-hoa-don/quyen-options/?ma_ct=HD1

// Query Params:
// ma_ct: Mã chứng từ (VD: "HD1", "HD2")
```

## Cấu trúc Components (Updated)

### Components

- `CreateReceiptDialog.tsx`: Dialog wrapper sử dụng AritoDialog + AritoForm
- `CreateReceiptForm.tsx`: Form component sử dụng FormField và SearchField của Arito
- `ActionBar.tsx`: Thanh công cụ với selection counter
- `BasicInfo.tsx`: Form tìm kiếm/filter hóa đơn
- `FormDialog.tsx`: Dialog wrapper cho form tìm kiếm

### Hooks

- `useCreateReceipt.ts`: Hook gọi API tạo phiếu thu
- `useCreateReceiptFormState.ts`: Hook quản lý state form tạo phiếu thu
- `useTaoPhieuThuTuHoaDon.ts`: Hook gọi API filter hóa đơn với pagination
- `useDialogState.ts`: Hook quản lý state dialog tìm kiếm
- `useFormFieldState.ts`: Hook quản lý state các field form

### Schemas

- `CreateReceiptFormValues`: Schema cho form tạo phiếu thu
- `CreateReceiptFromInvoiceFormValues`: Schema cho form filter hóa đơn

### Features

- ✅ Checkbox selection cho hóa đơn
- ✅ Server-side pagination (100 records/page)
- ✅ Selection counter trong ActionBar
- ✅ Disable buttons khi chưa chọn items
- ✅ Form validation với Zod
- ✅ SearchField components cho lookup
- ✅ Error handling và loading states
- ✅ Sử dụng AritoDialog, AritoForm, BottomBar components
- ✅ FormField với type checkbox, select, text, date
- ✅ UnitDropdown cho chọn đơn vị
