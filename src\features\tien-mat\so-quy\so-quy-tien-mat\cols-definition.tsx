import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { formatDate, formatMoney } from '@/lib/formatUtils';

// Helper function to format date from yyyymmdd to dd/MM/yyyy using date-fns
const formatDateFromYYYYMMDD = (dateString: string | null | undefined): string => {
  if (!dateString) return '';

  try {
    // Handle yyyymmdd format (e.g., "20240315" -> "15/03/2024")
    if (typeof dateString === 'string' && dateString.length === 8 && /^\d{8}$/.test(dateString)) {
      const year = parseInt(dateString.substring(0, 4));
      const month = parseInt(dateString.substring(4, 6)) - 1; // Month is 0-indexed in Date
      const day = parseInt(dateString.substring(6, 8));

      const date = new Date(year, month, day);
      return format(date, 'dd/MM/yyyy');
    }

    // Fallback to existing formatDate function for other formats
    return formatDate(dateString);
  } catch (error) {
    return '';
  }
};

export const cashBookColumns: GridColDef[] = [
  {
    field: 'unit_id',
    headerName: 'Đơn vị',
    width: 90
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 140,
    renderCell: params => formatDateFromYYYYMMDD(params.row.ngay_ct)
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 140
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 130
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 180
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    cellClassName: (params: any) => (params.row?.stt < 4 ? 'font-extrabold' : '')
  },
  {
    field: 'tk_du',
    headerName: 'TK đối ứng',
    width: 100
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 100,
    align: 'right',
    headerAlign: 'right'
  },
  {
    field: 'ty_gia',
    headerName: 'Tỷ giá',
    width: 100,
    align: 'right',
    renderCell: params => formatMoney(params.row.ty_gia),
    cellClassName: (params: any) => (params.row?.stt < 4 ? 'font-extrabold' : '')
  },
  {
    field: 'ps_no',
    headerName: 'PS nợ',
    width: 120,
    align: 'right',
    renderCell: params => formatMoney(params.row.ps_no),
    cellClassName: (params: any) => (params.row?.stt < 4 ? 'font-extrabold' : '')
  },
  {
    field: 'ps_co',
    headerName: 'PS có',
    width: 120,
    align: 'right',
    renderCell: params => formatMoney(params.row.ps_co),
    cellClassName: (params: any) => (params.row?.stt < 4 ? 'font-extrabold' : '')
  },
  {
    field: 'du_tt',
    headerName: 'Số dư',
    width: 120,
    align: 'right',
    renderCell: params => formatMoney(params.row.so_du),
    cellClassName: (params: any) => (params.row?.stt < 4 ? 'font-extrabold' : '')
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 100
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 100
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 110
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 100
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 90
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 130
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 140
  },
  {
    field: 'ma_ct',
    headerName: 'Mã c/từ',
    width: 100
  }
];

export const getFixedRowsData = () => [
  {
    id: '1',
    chi_tieu: 'Đầu kỳ',
    isGroup: true
  },
  {
    id: '2',
    chi_tieu: 'Tổng phát sinh',
    isGroup: true
  },
  {
    id: '3',
    chi_tieu: 'Cuối kỳ',
    isGroup: true
  }
];

export const renderCell = (params: any) => {
  const { row, field, value } = params;

  if (field === 'chi_tieu') {
    return (
      <div
        style={{
          fontWeight: row.isGroup ? 'bold' : 'normal',
          paddingLeft: row.id.length > 2 ? '20px' : '0px'
        }}
      >
        {value}
      </div>
    );
  }

  return value?.toLocaleString() || '';
};

export const DEFAULT_PRESETS: any = {
  today: 'Hôm nay',
  thisWeek: 'Tuần này',
  thisMonth: 'Tháng này',
  currentToDate: 'Đầu tháng đến hiện tại',
  prevMonth: 'Tháng trước',
  nextMonth: 'Tháng sau',
  month1: 'Tháng 1',
  month2: 'Tháng 2',
  month3: 'Tháng 3',
  month4: 'Tháng 4',
  month5: 'Tháng 5',
  month6: 'Tháng 6',
  month7: 'Tháng 7',
  month8: 'Tháng 8',
  month9: 'Tháng 9',
  month10: 'Tháng 10',
  month11: 'Tháng 11',
  month12: 'Tháng 12',
  quarter1: 'Quý 1',
  quarter2: 'Quý 2',
  quarter3: 'Quý 3',
  quarter4: 'Quý 4',
  thisQuarter: 'Quý này',
  prevQuarter: 'Quý trước',
  quarterToDate: 'Đầu quý đến hiện tại',
  thisYear: 'Năm nay',
  prevYear: 'Năm trước',
  yearToDate: 'Đầu năm đến hiện tại'
};

export const exportPrintColColums: GridColDef[] = [
  { field: 'col', headerName: 'Cột', width: 150 },
  { field: 'colName', headerName: 'Tên cột', width: 150 },
  { field: 'colNameEng', headerName: 'Tên tiếng anh', width: 150 },
  { field: 'colWidth', headerName: 'Độ rộng', width: 150 },
  { field: 'colType', headerName: 'Định dạng', width: 150 },
  { field: 'colAlign', headerName: 'Căn chỉnh', width: 150 },
  { field: 'colBold', headerName: 'In đậm', width: 150 },
  { field: 'colItalic', headerName: 'In nghiêng', width: 150 },
  { field: 'colSum', headerName: 'Tổng', width: 150 }
];
