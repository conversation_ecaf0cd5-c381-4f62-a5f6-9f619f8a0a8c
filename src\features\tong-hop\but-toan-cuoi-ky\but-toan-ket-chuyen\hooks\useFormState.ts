import { useState } from 'react';
import { GeneralJournalSchema, initialGeneralJournalValues } from '../schemas';

/**
 * Hook for managing form state
 * @returns Form state and handlers
 */
export const useFormState = () => {
  // Form values
  const [formValues, setFormValues] = useState<GeneralJournalSchema>(initialGeneralJournalValues);

  // Form mode (add, edit, view)
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');

  // Form errors
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Handle form field change
   * @param field Field name
   * @param value Field value
   */
  const handleFieldChange = (field: keyof GeneralJournalSchema, value: any) => {
    setFormValues(prev => ({
      ...prev,
      [field]: value
    }));
  };

  /**
   * Reset form to initial values
   */
  const resetForm = () => {
    setFormValues(initialGeneralJournalValues);
    setFormErrors({});
  };

  /**
   * Validate form
   * @returns Whether the form is valid
   */
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Required fields
    if (!formValues.ky1) {
      errors.ky1 = 'Kỳ không được để trống';
    }

    if (!formValues.ky2) {
      errors.ky2 = 'Kỳ không được để trống';
    }

    // Set errors
    setFormErrors(errors);

    // Return whether the form is valid
    return Object.keys(errors).length === 0;
  };

  /**
   * Handle form submission
   * @param onSubmit Callback to execute on successful submission
   * @returns Submit handler
   */
  const handleSubmit = (onSubmit: (values: GeneralJournalSchema) => void) => {
    return async () => {
      // Validate form
      const isValid = validateForm();

      if (!isValid) {
        return;
      }

      // Set submitting state
      setIsSubmitting(true);

      try {
        // Call onSubmit callback
        await onSubmit(formValues);

        // Reset form on successful submission
        resetForm();
      } catch (error) {
        console.error('Error submitting form:', error);

        // Handle submission error
        // You could set a general form error here
      } finally {
        // Reset submitting state
        setIsSubmitting(false);
      }
    };
  };

  return {
    formValues,
    formMode,
    formErrors,
    isSubmitting,
    handleFieldChange,
    resetForm,
    validateForm,
    handleSubmit
  };
};
