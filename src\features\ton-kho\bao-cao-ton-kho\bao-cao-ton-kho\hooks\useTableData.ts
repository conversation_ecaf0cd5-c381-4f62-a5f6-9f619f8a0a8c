import { useState, useEffect } from 'react';
import { useBaoCaoTonKho, BaoCaoTonKhoItem } from './useBaoCaoTonKho';
import { baoCaoTonKhoColumns } from '../cols-definition';
import { SearchFormValues } from '../schemas';

interface TableData {
  name: string;
  columns: any[];
  rows: any[];
}

// Định nghĩa kiểu dữ liệu cho dòng dữ liệu báo cáo tồn kho (map từ API response)
interface InventoryReportRow extends BaoCaoTonKhoItem {
  id: string | number;
  isTotalRow?: boolean;
}

export function useTableData(searchParams: SearchFormValues | null) {
  const [tables, setTables] = useState<TableData[]>([]);
  const [inventoryReportData, setInventoryReportData] = useState<InventoryReportRow[]>([]);
  const { data, isLoading, fetchData } = useBaoCaoTonKho(searchParams || {});

  useEffect(() => {
    if (!searchParams) return;
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  useEffect(() => {
    if (data && data.length > 0) {
      const mappedRows: InventoryReportRow[] = data.map((item, index) => ({
        ...item,
        id: index,
        isTotalRow: item.ten_vt === 'Tổng cộng'
      }));

      const tableData: TableData[] = [
        {
          name: 'Báo cáo tồn kho',
          columns: baoCaoTonKhoColumns(),
          rows: mappedRows
        }
      ];

      setTables(tableData);
      setInventoryReportData(mappedRows);
    } else {
      setTables([]);
      setInventoryReportData([]);
    }
  }, [data]);

  return { tables, loading: isLoading, inventoryReportData };
}
