import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  khachHangSearchColumns,
  boPhanSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  accountSearchColumns,
  nganHangSearchColumns,
  hoaDonSearchColumns,
  customerSearchColumns,
  thueSearchColumns,
  tinhChatThueSearchColumns,
  mauSoHoaDonSearchColumns,
  taiKhoanSearchColumns,
  taiKhoanNganHangSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  KhachHang,
  DotThanhToan,
  HopDong,
  KheUoc,
  Phi,
  VatTu,
  VuViec,
  NganHang,
  type Tax,
  type Tinh<PERSON>hatThue,
  type <PERSON><PERSON>hoan,
  type Tai<PERSON>hoanNganHang
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  ma_ngv: string,
  dien_giai: string | null,
  displayTax: boolean,
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 250,
    renderCell: params => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || dien_giai || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_data', row)}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 230,
    renderCell: params => <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name} />
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 150,
    renderCell: params => (
      <CellField
        name='du_cn'
        type='number'
        value={params.row.du_cn}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'du_cn', newValue)}
      />
    )
  },
  ...(ma_ngv === '4'
      ? [
          {
            field: 'tknh2',
            headerName: 'Ngân hàng',
            width: 170,
            renderCell: (params: any) => (
              <SearchField<TaiKhoanNganHang>
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}/`}
                searchColumns={taiKhoanNganHangSearchColumns}
                columnDisplay='tknh'
                dialogTitle='Danh mục tài khoản'
                value={params.row.tknh2_data?.tknh || params.row.tknh2_data?.code}
                onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tknh2_data', row)}
              />
            )
          }
        ]
      : []),
  ...(ma_ngv !== '1'
    ? [
        {
          field: 'tk_no',
          headerName: 'Tài khoản nợ',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_no_data?.code}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_no_data', row)}
            />
          )
        }
      ]
    : []),
  ...(ma_ngv === '1'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hoá đơn',
          width: 180,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${params.row.ma_kh_data?.uuid}&context=PC`}
              searchColumns={hoaDonSearchColumns}
              columnDisplay='so_ct'
              dialogTitle='Hoá đơn'
              disabled={!params.row.ma_kh_data?.uuid}
              value={params.row.id_hd_data?.so_ct || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'id_hd_data', row)}
            />
          )
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hoá đơn',
          width: 120,
          renderCell: (params: any) => <CellField name='so_ct0_hd' type='text' value={params.row.id_hd_data?.so_hd} />
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hoá đơn',
          width: 160,
          renderCell: (params: any) => (
            <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_hd} />
          )
        },
        {
          field: 'tk_no',
          headerName: 'Tài khoản nợ',
          width: 100,
          renderCell: (params: any) => params.row.id_hd_data?.tk_data?.tk
        },
        {
          field: 'ma_ngt_hd',
          headerName: 'Ngoại tệ',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='ma_ngt_hd' type='text' value={params.row.id_hd_data?.ngoai_te} />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 120,
          renderCell: (params: any) => <CellField name='ty_gia_hd' type='number' value={params.row.ty_gia_hd || 1} />
        },
        {
          field: 'tien_tren_hd',
          headerName: 'Tiền trên hóa đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='tien_tren_hd' type='number' value={params.row.id_hd_data?.tien_tren_hd || 0} />
          )
        },
        {
          field: 'da_pb',
          headerName: 'Đã phân bổ',
          width: 120
        },
        {
          field: 'con_lai',
          headerName: 'Còn lại',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='da_pb_nt' type='number' value={params.row.id_hd_data?.tien_con_phai_tt} />
          )
        }
      ]
    : []),
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    renderCell: (params: any) => (
      <CellField
        name='tien_hd_nt'
        type='number'
        value={params.row.tien_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  ...(ma_ngv === '2' || ma_ngv === '3'
    ? [
        {
          field: 'ma_loai_hd',
          headerName: 'Loại hoá đơn',
          width: 220,
          renderCell: (params: any) => (
            <CellField
              name='ma_loai_hd'
              type='select'
              value={params.row.ma_loai_hd}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_loai_hd', newValue)}
              options={[
                {
                  value: '0',
                  label: '0. Không có hóa đơn'
                },
                {
                  value: '1',
                  label: '1. Hóa đơn GTGT'
                },
                {
                  value: '2',
                  label: '2. Hóa đơn bán hàng thông thường'
                }
              ]}
            />
          )
        }
      ]
    : []),
  ...(displayTax && (ma_ngv == '2' || ma_ngv == '3')
    ? [
        {
          field: 'ma_thue',
          headerName: 'Mã thuế',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<Tax>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.THUE}/`}
              searchColumns={thueSearchColumns}
              columnDisplay='ma_thue'
              dialogTitle='Danh mục thuế'
              value={params.row.ma_thue_data?.ma_thue || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_thue_data', row)}
            />
          )
        },
        {
          field: 'ten_thue',
          headerName: 'Tên thuế',
          width: 150,
          renderCell: (params: any) => (
            <CellField name='ten_thue' type='text' value={params.row.ma_thue_data?.ten_thue || ''} />
          )
        },
        {
          field: 'tk_thue',
          headerName: 'Tk thuế',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<AccountModel>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              columnDisplay='code'
              dialogTitle='Danh mục tài khoản'
              value={params.row.tk_thue_data?.code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_data', row)}
            />
          )
        },
        {
          field: 'so_ct0',
          headerName: 'Số hoá đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='so_ct0'
              type='text'
              value={params.row.so_ct0 || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct0', newValue)}
            />
          )
        },
        {
          field: 'so_ct2',
          headerName: 'Ký hiệu',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='so_ct2'
              type='text'
              value={params.row.so_ct2 || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_ct2', newValue)}
            />
          )
        },
        {
          field: 'ngay_ct0',
          headerName: 'Ngày hoá đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='ngay_ct0'
              type='date'
              value={params.row.ngay_ct0 || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ngay_ct0', newValue)}
            />
          )
        },
        {
          field: 'ma_mau_ct',
          headerName: 'Mẫu HĐ',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.MAU_SO_HOA_DON}/`}
              searchColumns={mauSoHoaDonSearchColumns}
              columnDisplay='ma_mau_so'
              dialogTitle='Danh mục mẫu chứng từ'
              value={params.row.ma_mau_ct_data?.ma_mau_so || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_mau_ct_data', row)}
            />
          )
        },
        // ma_mau_bc - Mã mẫu báo cáo
        {
          field: 'ma_mau_bc',
          headerName: 'Mẫu báo cáo',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='ma_mau_bc'
              type='select'
              options={[
                { value: '3', label: '3. Hóa đơn giá trị gia tăng' },
                { value: '4', label: '4. Hàng hóa, dịch vụ mua vào không có hóa đơn' },
                { value: '5', label: '5. Hóa đơn bán hàng thông thường' }
              ]}
              value={params.row.ma_mau_bc || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_mau_bc', newValue)}
            />
          )
        },
        {
          field: 'ma_tc_thue',
          headerName: 'Mã tính chất',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<TinhChatThue>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TINH_CHAT_THUE}/`}
              searchColumns={tinhChatThueSearchColumns}
              columnDisplay='ma_tc_thue'
              dialogTitle='Danh mục tính chất thuế'
              value={params.row.ma_tc_thue_data?.customer_code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_tc_thue_data', row)}
            />
          )
        },
        {
          field: 'ma_kh_thue',
          headerName: 'Mã ncc',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<KhachHang>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={customerSearchColumns}
              columnDisplay='customer_code'
              dialogTitle='Danh mục đối tượng'
              value={params.row.ma_kh_thue_data?.customer_code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh_thue_data', row)}
            />
          )
        },
        // ten_kh_thue - Tên khách hàng thuế
        {
          field: 'ten_kh_thue',
          headerName: 'Tên nhà cung cấp',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='ten_kh_thue'
              type='text'
              value={params.row.ma_kh_thue_data?.customer_name || params.row.ten_kh_thue || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_kh_thue', newValue)}
            />
          )
        },
        // dia_chi - Địa chỉ
        {
          field: 'dia_chi',
          headerName: 'Địa chỉ',
          width: 200,
          renderCell: (params: any) => (
            <CellField
              name='dia_chi'
              type='text'
              value={params.row.dia_chi || params.row.ma_kh_thue_data?.address || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'dia_chi', newValue)}
            />
          )
        },
        // ma_so_thue - Mã số thuế
        {
          field: 'ma_so_thue',
          headerName: 'Mã số thuế',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='ma_so_thue'
              type='text'
              value={params.row.ma_so_thue || params.row.ma_kh_thue_data?.tax_code || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ma_so_thue', newValue)}
            />
          )
        },
        // ten_vt_thue - Tên vật tư thuế
        {
          field: 'ten_vt_thue',
          headerName: 'Tên hàng hóa - dịch vụ',
          width: 150,
          renderCell: (params: any) => (
            <CellField
              name='ten_vt_thue'
              type='text'
              value={params.row.ten_vt_thue || ''}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt_thue', newValue)}
            />
          )
        },
        // thue_nt - Thuế ngoại tệ
        {
          field: 'thue_nt',
          headerName: 'Thuế NT',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='thue_nt'
              type='number'
              value={params.row.thue_nt || 0.0}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
            />
          )
        },
        {
          field: 'thue',
          headerName: 'Thuế VND',
          width: 120,
          renderCell: (params: any) => (
            <CellField
              name='thue'
              type='number'
              value={params.row.thue_nt || 0.0}
              onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue', newValue)}
            />
          )
        },
        {
          field: 'ma_kh9',
          headerName: 'Cục thuế',
          width: 120,
          renderCell: (params: any) => (
            <SearchField<KhachHang>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
              searchColumns={customerSearchColumns}
              columnDisplay='customer_code'
              dialogTitle='Danh mục đối tượng'
              value={params.row.ma_kh9_data?.customer_code || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kh9_data', row)}
            />
          )
        }
      ]
    : []),
  {
    field: 'so_ct_dn',
    headerName: 'Số đề nghị',
    width: 120,
  },
  {
    field: 'line_dn',
    headerName: 'Dòng ĐN',
    width: 100,
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || params.row.ma_bp_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || params.row.ma_vv_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || params.row.ma_hd_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 140,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || params.row.ma_dtt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || params.row.ma_ku_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || params.row.ma_phi_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || params.row.ma_sp_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Mã lệnh sản xuất',
    width: 140,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || params.row.ma_lsx_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: params => (
      <SearchField<ChiPhi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
        searchColumns={chiPhiSearchColumns}
        columnDisplay='ma_cp'
        dialogTitle='Danh mục chi phí'
        value={params.row.ma_cp0_data?.ma_cp || params.row.ma_cp0_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
