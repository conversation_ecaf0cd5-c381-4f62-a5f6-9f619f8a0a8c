import React from 'react';
import { AritoHeaderTabs, AritoIcon, AritoForm, BottomBar } from '@/components/custom/arito';

import { useSearchFieldStates } from '../hooks/useSearchFieldStates';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { GeneralTab, OtherTab, BasicInfo } from './tabs';
import { searchSchema, initialValues } from '../schema';
import { formatDateToYYYYMMDD } from '../utils';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();

  const handleSubmit = async (data: any) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();

    const combinedData = {
      ...data,
      ...searchFieldData,
      ngay_ct1: formatDateToYYYYMMDD(data.ngay_ct1),
      ngay_ct2: formatDateToYYYYMMDD(data.ngay_ct2),
      ma_lvt: data.ma_lvt || '',
      data_analysis_struct: data.data_analysis_struct === 'no_analysis' ? '' : data.data_analysis_struct
    };
    onSearch(combinedData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng kê hàng bán bị trả lại'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto' style={{ width: '800px', minWidth: '800px' }}>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'general',
                  label: 'Thông tin chung',
                  component: <GeneralTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab searchFieldStates={searchFieldStates} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='search' onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
