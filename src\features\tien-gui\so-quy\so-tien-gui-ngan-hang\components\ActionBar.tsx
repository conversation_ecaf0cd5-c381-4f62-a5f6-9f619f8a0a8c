import { <PERSON><PERSON>, <PERSON>er, Refresh<PERSON><PERSON>, Search, Sheet } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoMenuButton, AritoIcon } from '@/components/custom/arito';
import { formatDate } from '@/lib/formatUtils';
import { useNganHang } from '@/hooks/queries';
import { SearchFormValues } from '../schema';

interface ActionBarProps {
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportDataClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  className?: string;
  searchParams?: SearchFormValues;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  className,
  searchParams
}) => {
  const { nganHangs } = useNganHang();

  const getNganHangName = (uuid: string) => {
    if (!uuid) return '';
    const nganHang = nganHangs.find(nh => nh.uuid === uuid);
    return nganHang ? `${nganHang.ma_ngan_hang} - ${nganHang.ten_ngan_hang}` : uuid;
  };
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Số tiền gửi ngân hàng</h1>
          <div className='text-[10px] text-gray-500'>
            <p>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              <span className='font-semibold'>
                Ngân hàng: {getNganHangName(searchParams?.tknh || '')}, từ ngày{' '}
                {formatDate(searchParams?.ngay_ct1?.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') || '')} đến ngày{' '}
                {formatDate(searchParams?.ngay_ct2?.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') || '')}
              </span>
            </p>
          </div>
        </div>
      }
    >
      {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />}
      {<AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} variant='secondary' />}
      {onRefreshClick && (
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} variant='secondary' />
      )}
      {onFixedColumnsClick && (
        <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} variant='secondary' />
      )}
      {onExportDataClick && (
        <AritoActionButton title='Kết xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Chỉnh sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
