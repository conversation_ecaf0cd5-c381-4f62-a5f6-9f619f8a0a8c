import { GridColDef } from '@mui/x-data-grid';
import { Checkbox } from '@mui/material';

export const getClosingJournalEntryColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void,
  selectedRows: any[] = [],
  onRowSelect?: (row: any, checked: boolean) => void
): GridColDef[] => [
  {
    field: 'select',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    renderCell: params => (
      <Checkbox
        checked={selectedRows.some(row => row.uuid === params.row.uuid)}
        onChange={e => onRowSelect?.(params.row, e.target.checked)}
        size='small'
      />
    ),
    renderHeader: () => (
      <Checkbox
        size='small'
        onChange={() => {
          // Handle select all logic if needed
        }}
      />
    )
  },
  {
    field: 'stt',
    headerName: 'Stt',
    width: 100
  },
  {
    field: 'ten_btkc',
    headerName: 'Tên bút toán',
    width: 250
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 200
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 200
  },
  {
    field: 'loai_bt',
    headerName: 'Loại kết chuyển',
    width: 200
  },
  {
    field: 'kc_ct_yn',
    headerName: 'Chi tiết',
    width: 100,
    renderCell: params => <Checkbox className='mx-auto -mt-1 flex' checked={params.row.check} onChange={() => {}} />
  }
];

export const getOtherJournalEntryColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void,
  selectedRows: any[] = [],
  onRowSelect?: (row: any, checked: boolean) => void
): GridColDef[] => [
  {
    field: 'select',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    renderCell: params => (
      <Checkbox
        checked={selectedRows.some(row => row.uuid === params.row.uuid)}
        onChange={e => onRowSelect?.(params.row, e.target.checked)}
        size='small'
      />
    )
  },
  {
    field: 'stt',
    headerName: 'Stt',
    width: 100
  },
  {
    field: 'ten_btkc',
    headerName: 'Tên bút toán',
    width: 250
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 200
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 200
  },
  {
    field: 'loai_bt',
    headerName: 'Loại kết chuyển',
    width: 200
  },
  {
    field: 'kc_ct_yn',
    headerName: 'Chi tiết',
    width: 100,
    renderCell: params => <Checkbox className='mx-auto -mt-1 flex' checked={params.row.check} onChange={() => {}} />
  }
];

// Default exports without selection logic (for backward compatibility)
export const exportMainColumns = getClosingJournalEntryColumns(
  () => {},
  () => {}
);

export const exportOtherColumns = getOtherJournalEntryColumns(
  () => {},
  () => {}
);
