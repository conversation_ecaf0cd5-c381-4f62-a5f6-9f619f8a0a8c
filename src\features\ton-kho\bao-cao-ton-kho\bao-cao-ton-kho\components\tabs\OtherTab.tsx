import React from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField, SearchField } from '@/components/custom/arito';
import { dvtSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

interface Props {
  searchFieldStates?: any;
}

const OtherTab: React.FC<Props> = ({ searchFieldStates }) => {
  return (
    <div className='min-h-[320px] p-6'>
      <div className='grid grid-cols-1 gap-x-4 gap-y-2'>
        {/* Quy đổi đvt field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Quy đổi đvt:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}/`}
              searchColumns={dvtSearchColumns}
              dialogTitle='Danh mục đơn vị tính'
              columnDisplay='dvt'
              displayRelatedField='ten_dvt'
              value={searchFieldStates?.unit?.dvt || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setUnit(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Nhóm theo</label>
          <div className='flex w-full items-center gap-2'>
            <div className='w-2/3'>
              <FormField
                name='group_by'
                type='select'
                className='w-full'
                options={[
                  { value: 0, label: 'Không phân nhóm' },
                  { value: 310, label: 'Loại vật tư' },
                  { value: 320, label: 'Nhóm vật tư 1' },
                  { value: 330, label: 'Nhóm vật tư 2' },
                  { value: 340, label: 'Nhóm vật tư 3' },
                  { value: 350, label: 'Tài khoản vật tư' }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Mẫu phân tích DL field */}
        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mẫu phân tích DL</label>
          <div className='flex w-full items-center gap-2'>
            <div className='w-2/3'>
              <FormField
                name='data_analysis_struct'
                type='select'
                className='w-full'
                options={[
                  { value: '0', label: 'Không phân tích' },
                  { value: '1', label: 'Mẫu phân tích tồn theo kho (Số lượng)' },
                  { value: '2', label: 'Mẫu phân tích tồn theo kho (số lượng, giá trị)' }
                ]}
              />
            </div>
            <div className='flex h-9 w-9 flex-shrink-0 items-center justify-center'>
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mẫu phân tích mới',
                    icon: 7,
                    onClick: () => console.log('Save new analysis template')
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Sửa mẫu đang chọn',
                    icon: 9,
                    onClick: () => console.log('Edit current analysis template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current analysis template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtherTab;
