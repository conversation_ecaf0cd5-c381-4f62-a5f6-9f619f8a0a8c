import { useState } from 'react';
import {
  Account<PERSON>odel,
  Han<PERSON>hanh<PERSON><PERSON>,
  KhachHang,
  Nhan<PERSON><PERSON>,
  Ngoa<PERSON><PERSON><PERSON>,
  Don<PERSON>iCo<PERSON>o,
  <PERSON><PERSON><PERSON><PERSON>,
  PhuongTienVan<PERSON>huyen,
  PhuongThucGiaoHang,
  type QuyenChungTu
} from '@/types/schemas';

export interface FormFieldState {
  // Customer information
  khachHang: KhachHang | null;
  nhanVien: NhanVien | null;
  taiKhoan: AccountModel | null;
  hanThanhToan: HanThanhToan | null;

  // Document information
  quyenChungTu: QuyenChungTu | null;
  quyenPhieuNhap: QuyenChungTu | null;
  ngoaiTe: NgoaiTe | null;

  // Other information
  diaChi: DiaChi | null;
  phuongTienVanChuyen: PhuongTienVanChuyen | null;
  phuongTienGiaoHang: PhuongThucGiaoHang | null;
  cucThue: KhachHang | null;

  soChungTu: string | null;
  soPhieuNhap: string | null;

  // Checkbox states
  hdmh_yn: boolean;
  pn_yn: boolean;
  pc_tao_yn: boolean;
  ck_yn: boolean;
  xt_yn: boolean;
  loai_ck: string;

  // Payment method
  ma_httt: string;
}

export interface FormFieldActions {
  // Search field setters
  setKhachHang: (khachHang: KhachHang) => void;
  setNhanVien: (nhanVien: NhanVien) => void;
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setHanThanhToan: (hanThanhToan: HanThanhToan) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setQuyenPhieuNhap: (quyenPhieuNhap: QuyenChungTu) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;

  setDiaChi: (diaChi: DiaChi) => void;
  setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => void;
  setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => void;
  setCucThue: (cucThue: KhachHang) => void;

  setSoChungTu: (soChungTu: string) => void;
  setSoPhieuNhap: (soPhieuNhap: string) => void;

  // Checkbox setters
  setHdmhYn: (value: boolean) => void;
  setPnYn: (value: boolean) => void;
  setPcTaoYn: (value: boolean) => void;
  setCkYn: (value: boolean) => void;
  setXtYn: (value: boolean) => void;
  setLoaiCk: (value: string) => void;

  // Payment method setter
  setMaHttt: (value: string) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  // Customer information
  khachHang: null,
  nhanVien: null,
  taiKhoan: null,
  hanThanhToan: null,

  // Document information
  quyenChungTu: null,
  quyenPhieuNhap: null,
  ngoaiTe: null,

  // Other information
  diaChi: null,
  phuongTienVanChuyen: null,
  phuongTienGiaoHang: null,
  cucThue: null,

  soChungTu: null,
  soPhieuNhap: null,

  // Checkbox states
  hdmh_yn: true,
  pn_yn: true,
  pc_tao_yn: false,
  ck_yn: false,
  xt_yn: false,
  loai_ck: '2',

  // Payment method
  ma_httt: 'TMB'
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    // Customer information
    khachHang: initialData.ma_kh_data || null,
    nhanVien: initialData.ma_nvmh_data || null,
    taiKhoan: initialData.tk_data || null,
    hanThanhToan: initialData.ma_tt_data || null,

    // Document information
    quyenChungTu: initialData.ma_nk_data || null,
    quyenPhieuNhap: initialData.ma_nk_pn_data || null,
    ngoaiTe: initialData.ma_nt_data || null,

    soChungTu: initialData.so_ct || null,
    soPhieuNhap: initialData.so_pn || null,

    // Checkbox states
    hdmh_yn: initialData.hdmh_yn ?? true,
    pn_yn: initialData.pn_yn ?? true,
    pc_tao_yn: initialData.pc_tao_yn ?? false,
    ck_yn: initialData.ck_yn ?? false,
    xt_yn: initialData.xt_yn ?? false,
    loai_ck: initialData.loai_ck || '2',

    // Payment method
    ma_httt: initialData.ma_httt || 'TMB'
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setNhanVien: (nhanVien: NhanVien) => {
      setState(prev => ({
        ...prev,
        nhanVien
      }));
    },

    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({
        ...prev,
        taiKhoan
      }));
    },

    setHanThanhToan: (hanThanhToan: HanThanhToan) => {
      setState(prev => ({
        ...prev,
        hanThanhToan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setQuyenPhieuNhap: (quyenPhieuNhap: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenPhieuNhap
      }));
    },

    setNgoaiTe: (ngoaiTe: NgoaiTe) => {
      setState(prev => ({
        ...prev,
        ngoaiTe
      }));
    },

    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({
        ...prev,
        soChungTu
      }));
    },

    setSoPhieuNhap: (soPhieuNhap: string) => {
      setState(prev => ({
        ...prev,
        soPhieuNhap
      }));
    },

    setDiaChi: (diaChi: DiaChi) => {
      setState(prev => ({
        ...prev,
        diaChi
      }));
    },

    setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => {
      setState(prev => ({
        ...prev,
        phuongTienVanChuyen
      }));
    },

    setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => {
      setState(prev => ({
        ...prev,
        phuongTienGiaoHang
      }));
    },

    setCucThue: (cucThue: KhachHang) => {
      setState(prev => ({
        ...prev,
        cucThue
      }));
    },

    // Checkbox setters
    setHdmhYn: (value: boolean) => {
      setState(prev => ({ ...prev, hdmh_yn: value }));
    },

    setPnYn: (value: boolean) => {
      setState(prev => ({ ...prev, pn_yn: value }));
    },

    setPcTaoYn: (value: boolean) => {
      setState(prev => ({ ...prev, pc_tao_yn: value }));
    },

    setCkYn: (value: boolean) => {
      setState(prev => ({ ...prev, ck_yn: value }));
    },

    setXtYn: (value: boolean) => {
      setState(prev => ({ ...prev, xt_yn: value }));
    },

    setLoaiCk: (value: string) => {
      setState(prev => ({ ...prev, loai_ck: value }));
    },

    // Payment method setter
    setMaHttt: (value: string) => {
      setState(prev => ({ ...prev, ma_httt: value }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
