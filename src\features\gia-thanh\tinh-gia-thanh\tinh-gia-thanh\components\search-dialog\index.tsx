import React from 'react';
import { BottomBar, AritoIcon, AritoDialog, AritoForm } from '@/components/custom/arito';
import { SearchFormValues, SearchFormSchema, initialSearchValues } from '../../schema';
import { BasicInfoTab } from './BasicInfoTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: any) => void;
}

const SearchDialog = ({ openSearchDialog, onCloseSearchDialog, onSearch }: SearchDialogProps) => {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Tính giá thành'
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={SearchFormSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar onClose={onCloseSearchDialog} mode='search' />}
      />
    </AritoDialog>
  );
};

export default SearchDialog;
