import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';
import { useDonViCoSo } from '@/hooks';

interface BasicInfoTabProps {
  formMode: FormMode;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  const { donViCoSos } = useDonViCoSo();
  const donViOptions = donViCoSos.map(donVi => ({
    value: donVi.uuid,
    label: `${donVi.ma_unit} - ${donVi.ten_unit}`
  }));

  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Kỳ</Label>
          <FormField type='number' name='period' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Năm</Label>
          <FormField type='number' name='year' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Đơn vị</Label>
          <div className='w-full'>
            <FormField type='select' name='unit' options={donViOptions} disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};
