/**
 * TypeScript interface for KhaiBaoNghiepVuKeToan (Accounting Business Operation Declaration) model
 *
 * This interface represents the structure of the KhaiBaoNghiepVuKeToan model from the backend.
 * It defines business operation declarations used for accounting purposes.
 */

import { ApiResponse } from '../api.type';

export interface KhaiBaoNghiepVuKeToan {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Business operation code
   */
  ma_ngvkt: string;

  /**
   * Business operation name
   */
  ten_ngvkt: string;

  /**
   * Alternative business operation name (optional)
   */
  ten_ngvkt2?: string | null;

  /**
   * Debit account UUID
   */
  tk_no?: string;

  /**
   * Debit account data (populated by serializer)
   */
  tk_no_data?: any;

  /**
   * Credit account UUID
   */
  tk_co?: string;

  /**
   * Credit account data (populated by serializer)
   */
  tk_co_data?: any;

  /**
   * Department UUID (optional)
   */
  ma_bp?: string | null;

  /**
   * Department data (populated by serializer)
   */
  ma_bp_data?: any;

  /**
   * Case/Job UUID (optional)
   */
  ma_vv?: string | null;

  /**
   * Case/Job data (populated by serializer)
   */
  ma_vv_data?: any;

  /**
   * Contract UUID (optional)
   */
  ma_hd?: string | null;

  /**
   * Contract data (populated by serializer)
   */
  ma_hd_data?: any;

  /**
   * Agreement UUID (optional)
   */
  ma_ku?: string | null;

  /**
   * Agreement data (populated by serializer)
   */
  ma_ku_data?: any;

  /**
   * Fee UUID (optional)
   */
  ma_phi?: string | null;

  /**
   * Fee data (populated by serializer)
   */
  ma_phi_data?: any;

  /**
   * Notes or description (optional)
   */
  ghi_chu?: string | null;

  /**
   * Status indicator (1=active, 0=inactive)
   */
  status: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Input type for creating/updating KhaiBaoNghiepVuKeToan
 */
export interface KhaiBaoNghiepVuKeToanInput {
  ma_ngvkt: string;
  ten_ngvkt: string;
  ten_ngvkt2?: string | null;
  tk_no?: string;
  tk_co?: string;
  ma_bp?: string | null;
  ma_vv?: string | null;
  ma_hd?: string | null;
  ma_ku?: string | null;
  ma_phi?: string | null;
  ghi_chu?: string | null;
  status: number;
}

/**
 * Type for KhaiBaoNghiepVuKeToan API response
 */
export type KhaiBaoNghiepVuKeToanResponse = ApiResponse<KhaiBaoNghiepVuKeToan>;
