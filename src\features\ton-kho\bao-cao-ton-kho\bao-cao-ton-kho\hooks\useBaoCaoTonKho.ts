import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

export interface BaoCaoTonKhoItem {
  stt: number;
  ma_kho: string;
  ma_vt: string;
  nhom: string;
  dvt: string;
  ton_cuoi: string;
  du_cuoi: string;
  ten_vt: string;
  ten_kho: string;
}

export interface BaoCaoTonKhoResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoTonKhoItem[];
}

export interface UseBaoCaoTonKhoReturn {
  data: BaoCaoTonKhoItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
  submitBaoCaoTonKho: (requestBody: any) => Promise<void>;
}

export function useBaoCaoTonKho(searchParams: any): UseBaoCaoTonKhoReturn {
  const { entity } = useAuth();
  const [data, setData] = useState<BaoCaoTonKhoItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (searchParams: any) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<BaoCaoTonKhoResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BAO_CAO_TON_KHO}/`,
          searchParams
        );

        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          setData(response.data.results);
        } else {
          setData([]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const submitBaoCaoTonKho = useCallback(
    async (requestBody: any) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.BAO_CAO_TON_KHO}/`, requestBody);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while submitting data';
        setError(new Error(errorMessage));
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    submitBaoCaoTonKho
  };
}
