import React from 'react';
import { SearchField, FormField } from '@/components/custom/arito';
import { khoHangSearchColumns, QUERY_KEYS } from '@/constants';
import { Label } from '@/components/ui/label';

interface Props {
  searchFieldStates?: any;
}

const BasicInfo: React.FC<Props> = ({ searchFieldStates }) => {
  return (
    <div className='p-6'>
      <div className='grid grid-cols-1 gap-x-6 gap-y-6'>
        {/* Đến ngày field */}
        <div className='flex items-center'>
          <FormField
            name='ngay_ct1'
            label='Đến ngày'
            type='date'
            className='w-full'
            labelClassName='min-w-32 text-left text-sm font-medium text-gray-700'
            inputClassName='w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500'
          />
        </div>

        {/* Mã kho field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Mã kho:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={khoHangSearchColumns}
              dialogTitle='Danh mục kho'
              columnDisplay='ma_kho'
              displayRelatedField='ten_kho'
              value={searchFieldStates?.warehouse?.ma_kho || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setWarehouse(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
