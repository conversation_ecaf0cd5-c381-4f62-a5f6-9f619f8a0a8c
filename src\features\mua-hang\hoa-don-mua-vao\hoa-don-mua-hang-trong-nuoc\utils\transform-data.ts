import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import type { ChiTietHoaDonMuaHangTrongNuocInput } from '@/types/schemas';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
const transformDetailRows = (detailRows: ChiTietHoaDonMuaHangTrongNuocInput[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) ? row.uuid : null,
    ma_vt: row.ma_vt_data?.uuid || '',
    dvt: row.ma_vt_data?.dvt || '',

    ma_kho: row.ma_kho_data?.uuid || '',
    so_luong: row.so_luong || 0,
    gia_nt0: row.gia_nt0 || 0,
    tien_nt0: row.tien_nt0 || row.so_luong * row.gia_nt0,
    cp_nt: row.cp_nt || 0,
    gia_nt: row.ma_vt_data?.gia_ton || 0,
    gia_ton: row.gia_nt || 0,

    ma_thue: row.ma_thue_data?.uuid || '',
    thue_nt: row.ma_vt_data?.thue_nt || (row.so_luong * row.gia_nt0 * row.ma_thue_data?.thue_suat) / 100,

    tk_cpxt: row.tk_cpxt_data?.uuid || '',
    tk_vt: row.tk_vt_data?.uuid || '',

    px_dd: row.px_dd || false,

    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null,

    so_ct_dh: row.so_ct_dh || '',
    line_dh: row.line_dh || index + 1,
    so_ct_hd4: row.so_ct_hd4 || '',
    line_dh4: row.line_dh4 || ''
  }));
};

const transformChiPhiRows = (chiPhiRows: any[]) => {
  return chiPhiRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) ? row.uuid : null,
    ma_cp: row.ma_cp_data?.uuid || null,
    ten_cp: row.ma_cp_data?.ten_cp || row.ten_cp || '',

    tien_cp_nt: row.tien_cp_nt || 0,
    tien_cp: row.tien_cp || 0,

    ma_kh: row.ma_kh_data?.uuid || null,
    ten_kh: row.ma_kh_data?.customer_name || row.ten_kh || '',

    tk: row.tk_data?.uuid || ''
  }));
};

const transformChiPhiChiTietRows = (chiPhiChiTietRows: any[]) => {
  return chiPhiChiTietRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) ? row.uuid : null,
    ma_cp: row.ma_cp_data?.uuid || null,
    ten_cp: row.ma_cp_data?.ten_cp || row.ten_cp || '',

    ma_vt: row.ma_vt_data?.uuid || null,
    ten_vt: row.ten_vt || '',

    tien_cp_nt: row.tien_cp_nt || 0,
    tien_cp: row.tien_cp || 0,

    line_vt: row.line_vt || 0,
    line_cp: row.line_cp || 0
  }));
};

const transformThueRows = (thueRows: any[]) => {
  return thueRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) ? row.uuid : null,
    so_ct0: row.so_ct0 || '',
    so_ct2: row.so_ct2 || null,
    ngay_ct0: format(row.ngay_ct0, 'yyyy-MM-dd') || '',

    ma_thue: row.ma_thue_data?.uuid || '',
    thue_suat: row.thue_suat || 0,
    ma_mau_bc: row.ma_mau_bc || '3',
    ma_tc_thue: row.ma_tc_thue || '1',

    ma_kh: row.ma_kh_data?.uuid || null,
    ten_kh_thue: row.ma_kh_data?.customer_name || row.ten_kh_thue || '',
    dia_chi: row.ma_kh_data?.address || row.dia_chi || '',
    ma_so_thue: row.ma_kh_data?.tax_code || row.ma_so_thue || '',

    ten_vt_thue: row.ten_vt_thue || '',

    t_tien_nt: row.t_tien_nt || 0,
    t_tien: row.t_tien || 0,

    tk_thue_no: row.tk_thue_no_data?.uuid || '',
    ten_tk_thue_no: row.tk_thue_no_data?.customer_name || '',

    t_thue_nt: row.t_thue_nt || 0,
    t_thue: row.t_thue || 0,
    ma_kh9: row.ma_kh9_data?.uuid || null,
    ma_tt: row.ma_tt_data?.uuid || null,
    ghi_chu: row.ghi_chu || '',
    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param accountRows - Array of account row data
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (data: any, state: FormFieldState, entityUnit: any, ...rest: any[]) => {
  const detail = transformDetailRows(rest[0]);
  const chiPhi = transformChiPhiRows(rest[1]);
  const chiPhiChiTiet = transformChiPhiChiTietRows(rest[2]);
  const thue = transformThueRows(rest[3]);

  return {
    ...data,
    unit_id: entityUnit?.uuid || '',

    hdmh_yn: state.hdmh_yn,
    pn_yn: state.pn_yn,
    pc_tao_yn: state.pc_tao_yn,
    xt_yn: state.xt_yn,
    ck_yn: state.ck_yn,
    loai_ck: state.loai_ck,

    ma_httt: state.ma_httt || 'TMB',

    ma_kh: state.khachHang?.uuid || '',
    ma_so_thue: data.ma_so_thue || state.khachHang?.tax_code || '',
    ten_kh: data.ten_kh || state.khachHang?.customer_name || '',
    dia_chi: data.dia_chi || state.khachHang?.address || '',
    ong_ba: data.ong_ba || state.khachHang?.contact_person || '',
    e_mail: data.e_mail || state.khachHang?.email || null,
    dien_giai: data.dien_giai || state.khachHang?.description || '',

    ma_nvmh: state.nhanVien?.uuid || state.khachHang?.sales_rep_data?.uuid || '',
    tk: state.taiKhoan?.uuid || state.khachHang?.account_data?.uuid || '',
    ma_tt: state.hanThanhToan?.uuid || state.khachHang?.payment_term_data?.uuid || '',

    ma_ngv: '1',
    i_so_pn: transformDocumentNumber(state.quyenPhieuNhap, state.soPhieuNhap || '', MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO)
      .i_so_ct,
    ma_nk_pn: transformDocumentNumber(state.quyenPhieuNhap, state.soPhieuNhap || '', MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO)
      .ma_nk,
    so_pn: transformDocumentNumber(state.quyenPhieuNhap, state.soPhieuNhap || '', MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO)
      .so_ct,
    ...transformDocumentNumber(
      state.quyenChungTu,
      state.soChungTu || '',
      MA_CHUNG_TU.MUA_HANG.HOA_DON_MUA_HANG_TRONG_NUOC
    ),

    // i_so_px: transformDocumentNumber(state.quyenPhieuNhap, state.soPhieuNhap || '', MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO)
    //   .so_ct,
    // so_px: state.soChungTu || '',
    so_ct2: data.so_ct2 || '',
    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_lct || new Date().toISOString().split('T')[0],

    so_ct_hddt0: data.so_ct_hddt0 || '',
    ngay_ct_hddt0: data.ngay_ct_hddt0 || '',
    so_ct2_hddt0: data.so_ct2_hddt0 || '',

    t_so_luong: rest[4].so_luong || 0,
    t_tien_nt: rest[4].tong_tien || 0,
    t_tien: data.t_tien || rest[4].tong_tien || 0,
    t_cp_nt: data.t_cp_nt || 0,
    t_cp: data.t_cp || data.t_cp_nt || 0,
    t_thue_nt: data.t_thue_nt || rest[4].thue || 0,
    t_thue: data.t_thue || data.t_thue_nt || rest[4].thue || 0,
    t_ck_nt: data.t_ck_nt || 0,
    t_ck: data.t_ck || data.t_ck_nt || 0,
    t_tt_nt: data.t_tt_nt || rest[4].tong_thanh_toan || 0,
    t_tt: data.t_tt || data.t_tt_nt || rest[4].tong_thanh_toan,

    chi_tiet_hoa_don: detail,
    chi_phi_hoa_don: chiPhi,
    chi_phi_chi_tiet_hoa_don: chiPhiChiTiet,
    thue_hoa_don: thue
  };
};
