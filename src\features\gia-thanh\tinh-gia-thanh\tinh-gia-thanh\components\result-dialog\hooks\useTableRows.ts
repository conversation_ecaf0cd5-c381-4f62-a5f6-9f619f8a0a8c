import { GridCellParams } from '@mui/x-data-grid';
import { useState, useEffect } from 'react';

export interface TableRow {
  uuid?: string | null;
  ma_vt?: string;
  ten_vt?: string;
  sl_nhap_kho?: string;
  tong_gia_thanh?: string;
  gia_thanh_don_vi?: string;
}

export interface SelectedCellInfo {
  id: string;
  field: string;
}

// Interface for the selectedObj structure
export interface SelectedObjData {
  step4_products?: TableRow[];
  step1_materials?: TableRow[];
  step2_products?: TableRow[];
  step3_combined?: TableRow[];
}

export function useTableRows(selectedObj: SelectedObjData[] = []) {
  const [rows, setRows] = useState<TableRow[]>([]);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);
  const [editPrint, setEditPrint] = useState<boolean>(false);

  // Transform selectedObj data into rows
  const transformSelectedObjToRows = (data: SelectedObjData[]): TableRow[] => {
    if (!data || data.length === 0) return [];

    const allRows: TableRow[] = [];

    data.forEach((item, index) => {
      // Process step4_products (main data)
      if (item.step4_products) {
        item.step4_products.forEach((product, productIndex) => {
          allRows.push({
            ...product,
            uuid: product.ma_vt || `step4-${index}-${productIndex}` // Use ma_vt as uuid or generate one
          });
        });
      }

      // You can add logic for other steps if needed
      if (item.step1_materials) {
        item.step1_materials.forEach((material, materialIndex) => {
          allRows.push({
            ...material,
            uuid: material.ma_vt || `step1-${index}-${materialIndex}`
          });
        });
      }

      if (item.step2_products) {
        item.step2_products.forEach((product, productIndex) => {
          allRows.push({
            ...product,
            uuid: product.ma_vt || `step2-${index}-${productIndex}`
          });
        });
      }

      if (item.step3_combined) {
        item.step3_combined.forEach((combined, combinedIndex) => {
          allRows.push({
            ...combined,
            uuid: combined.ma_vt || `step3-${index}-${combinedIndex}`
          });
        });
      }
    });

    return allRows;
  };

  // Update rows when selectedObj changes
  useEffect(() => {
    const transformedRows = transformSelectedObjToRows(selectedObj);
    setRows(transformedRows);

    // Clear selection when data changes
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  }, [selectedObj]);

  const handleAddRow = () => {
    const newRow: TableRow = {
      uuid: String(Math.random()),
      ma_vt: '',
      ten_vt: '',
      sl_nhap_kho: '0',
      tong_gia_thanh: '0',
      gia_thanh_don_vi: '0'
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid || null);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: TableRow[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid || null);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow: TableRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid || null);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow: TableRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid || null);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    // No need to update selectedRowUuid as it remains the same
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      [field]: newValue
    };

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: TableRow }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as TableRow);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  // Table action handlers
  const handleRefresh = () => {
    // Refresh the table data
    console.log('Refreshing table data...');
    // You can add actual refresh logic here, such as:
    // - Calling an API to reload data
    // - Resetting rows to initial state
    // - Triggering a parent component refresh callback
  };

  const handlePin = () => {
    // Pin/unpin functionality
    console.log('Pin/Unpin table...');
    // You can add pin logic here, such as:
    // - Saving table state to localStorage
    // - Marking table as pinned/unpinned
    // - Showing pinned indicator
  };

  const handleEdit = () => {
    setEditPrint(true);
  };

  const handleCloseEdit = () => {
    setEditPrint(false);
  };

  const handleExport = () => {
    // Export table data
    console.log('Exporting table data...');
    // You can add export logic here, such as:
    // - Exporting to Excel/CSV
    // - Generating PDF report
    // - Copying data to clipboard
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    editPrint,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleRefresh,
    handlePin,
    handleEdit,
    handleExport,
    handleCloseEdit,
    handleCellValueChange
  };
}
