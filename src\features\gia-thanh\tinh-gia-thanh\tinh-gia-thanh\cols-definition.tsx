import { GridColDef } from '@mui/x-data-grid';
import { Checkbox } from '@mui/material';

export const CostCalculationColumns = (openResultDialog: () => void): GridColDef[] => [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: params => <Checkbox onChange={e => {}} />,
    renderCell: params => (
      <Checkbox checked={params.row.selected} onChange={e => (params.row.selected = e.target.checked)} />
    )
  },
  {
    field: 'step',
    headerName: 'Bước tính',
    width: 100,
    renderCell: params => 'Bước 4'
  },
  {
    field: 'xu_ly_gia_thanh',
    headerName: 'Xử lý giá thành',
    width: 360,
    renderCell: params => (
      <div className='flex items-center gap-2'>
        <button onClick={openResultDialog} className='flex-1 text-center text-blue-500 hover:underline'>
          <PERSON>em kết qu<PERSON>
        </button>
      </div>
    )
  }
];

export const stageSearchColumns: GridColDef[] = [
  { field: 'ma_cong_doan', headerName: 'Mã công đoạn', flex: 1 },
  { field: 'ten_cong_doan', headerName: 'Tên công đoạn', flex: 2 }
];
