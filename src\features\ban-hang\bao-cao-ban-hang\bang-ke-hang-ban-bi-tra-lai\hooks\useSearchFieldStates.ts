import { useState } from 'react';

export function useSearchFieldStates() {
  const [employee, setEmployee] = useState<any>(null);
  const [customer, setCustomer] = useState<any>(null);
  const [customerGroup1, setCustomerGroup1] = useState<any>(null);
  const [customerGroup2, setCustomerGroup2] = useState<any>(null);
  const [customerGroup3, setCustomerGroup3] = useState<any>(null);
  const [region, setRegion] = useState<any>(null);
  const [product, setProduct] = useState<any>(null);
  const [productGroup1, setProductGroup1] = useState<any>(null);
  const [productGroup2, setProductGroup2] = useState<any>(null);
  const [productGroup3, setProductGroup3] = useState<any>(null);
  const [warehouse, setWarehouse] = useState<any>(null);

  const [transaction, setTransaction] = useState<any>(null);
  const [itemAccount, setItemAccount] = useState<any>(null);
  const [revenueAccount, setRevenueAccount] = useState<any>(null);
  const [costAccount, setCostAccount] = useState<any>(null);
  const [batch, setBatch] = useState<any>(null);
  const [location, setLocation] = useState<any>(null);

  const getSearchFieldData = () => {
    return {
      ma_nvbh: employee?.uuid || '',
      ma_kh: customer?.uuid || '',
      nh_kh1: customerGroup1?.uuid || '',
      nh_kh2: customerGroup2?.uuid || '',
      nh_kh3: customerGroup3?.uuid || '',
      rg_code: region?.uuid || '',
      ma_vt: product?.uuid || '',
      nh_vt1: productGroup1?.uuid || '',
      nh_vt2: productGroup2?.uuid || '',
      nh_vt3: productGroup3?.uuid || '',
      ma_kho: warehouse?.uuid || '',

      ma_gd: transaction?.uuid || '',
      tk_vt: itemAccount?.uuid || '',
      tk_dt: revenueAccount?.uuid || '',
      tk_gv: costAccount?.uuid || '',
      ma_lo: batch?.uuid || '',
      ma_vi_tri: location?.uuid || ''
    };
  };

  return {
    employee,
    setEmployee,
    customer,
    setCustomer,
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,
    region,
    setRegion,
    product,
    setProduct,
    productGroup1,
    setProductGroup1,
    productGroup2,
    setProductGroup2,
    productGroup3,
    setProductGroup3,
    warehouse,
    setWarehouse,

    transaction,
    setTransaction,
    itemAccount,
    setItemAccount,
    revenueAccount,
    setRevenueAccount,
    costAccount,
    setCostAccount,
    batch,
    setBatch,
    location,
    setLocation,

    getSearchFieldData
  };
}
