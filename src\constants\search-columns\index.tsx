import type { GridRenderCellParams } from '@mui/x-data-grid';
import type { ExtendedGridColDef } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';
export * from './nhom';
export * from './ccdc';
export * from './thue';
export * from './chu-i';

export const vatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 200 },
  { field: 'dvt_data', headerName: 'Đvt', width: 80, renderCell: (params: any) => params.row.dvt_data?.dvt || '' },
  { field: 'nh_vt1', headerName: 'Nhóm 1', width: 120 },
  {
    field: 'lo_yn',
    headerName: '<PERSON> dõi lô',
    width: 90,
    type: 'boolean',
    renderCell: (params: any) => <Checkbox checked={params.row.lo_yn} />
  },
  {
    field: 'qc_yn',
    headerName: 'Quy cách',
    width: 90,
    type: 'boolean',
    renderCell: (params: any) => <Checkbox checked={params.row.qc_yn} />
  },
  { field: 'image_id', headerName: 'Hình ảnh', width: 120 }
];

export const vatTu1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  ...vatTuSearchColumns
];

export const dvtSearchColumns: ExtendedGridColDef[] = [
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'ten_dvt', headerName: 'Tên đơn vị tính', width: 150 }
];

export const khachHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 250 },
  { field: 'cong_no_phai_thu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'cong_no_phai_tra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];

export const nhomKhachHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nh', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_nh', headerName: 'Tên nhóm', width: 250 }
];

export const ngoaiTeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 80 },
  { field: 'ten_nt', headerName: 'Tên ngoại tệ', width: 150 }
];

export const loaiGiaBanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai_gb', headerName: 'Mã loại giá', width: 150 },
  { field: 'ten_loai_gb', headerName: 'Tên loại giá', width: 250 }
];

export const accountSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 1 },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    flex: 1,
    renderCell: (params: any) => params?.row?.parent_account_code_data?.code || ''
  },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const taiKhoanSearchColumns: ExtendedGridColDef[] = [
  { field: 'code', headerName: 'Mã tài khoản', flex: 1 },
  { field: 'name', headerName: 'Tên tài khoản', flex: 1 },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    flex: 1,
    renderCell: (params: any) => params?.row?.parent_account_code_data?.code || ''
  },
  {
    field: 'tk_so_cai',
    headerName: 'Tk sổ cái',
    flex: 1,
    checkboxSelection: true
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'Tk chi tiết',
    flex: 1,
    checkboxSelection: true
  },
  { field: 'bac_tk', headerName: 'Bậc tk', flex: 1 }
];

export const accountListSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.checkbox} />;
    }
  },
  ...accountSearchColumns
];

export const sanPhamSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt_data', headerName: 'Đvt', width: 80, renderCell: (params: any) => params.row.dvt_data?.dvt || '' }
];

export const lenhSanXuatSearchColumns: ExtendedGridColDef[] = [
  { field: 'so_lsx', headerName: 'Số lệnh', width: 150 },
  { field: 'description', headerName: 'Diễn giải', width: 250 }
];

export const lenhSanXuat1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  ...lenhSanXuatSearchColumns
];

export const boPhanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 200 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 200 }
];

export const donViSearchColumns = [
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 120 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 250 }
];

export const bookSearchColumns = [
  { field: 'ma_quyen', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_quyen', headerName: 'Tên quyển', width: 250 },
  { field: 'so_khai_bao', headerName: 'Số khai báo', width: 150 }
];

export const chungTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 150 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 250 }
];

export const chungTu1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  ...chungTuSearchColumns
];

export const quyenChungTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nk', headerName: 'Mã quyển', width: 150 },
  { field: 'ten_nk', headerName: 'Tên quyển', width: 250 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', width: 150 }
];

export const yeuToSearchColumns = [
  { field: 'ma_yt', headerName: 'Mã yếu tố', width: 150 },
  { field: 'ten_yt', headerName: 'Tên yếu tố', width: 250 }
];

export const yeuTo1SearchColumns = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderCell: (params: any) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'ma_yt', headerName: 'Mã yếu tố', width: 150 },
  { field: 'ten_yt', headerName: 'Tên yếu tố', width: 250 }
];

export const taiSanSearchColumns = [
  { field: 'ma_ts', headerName: 'Mã tài sản', width: 150 },
  { field: 'ten_ts', headerName: 'Tên tài sản', width: 250 }
];

export const congDoanSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_bp',
    headerName: 'Mã công đoạn',
    width: 150,
    renderCell: (params: any) => params.row.ma_bp_data?.ma_bp || ''
  },
  {
    field: 'ten_bp',
    headerName: 'Tên công đoạn',
    width: 250,
    renderCell: (params: any) => params.row.ma_bp_data?.ten_bp || ''
  }
];

export const congDoan1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  ...congDoanSearchColumns
];

export const vuViecSearchColumns = [
  { field: 'ma_vu_viec', headerName: 'Mã vụ việc', width: 150 },
  { field: 'ten_vu_viec', headerName: 'Tên vụ việc', width: 250 }
];

export const phiSearchColumns = [
  { field: 'ma_phi', headerName: 'Mã phí', width: 150 },
  { field: 'ten_phi', headerName: 'Tên phí', width: 250 }
];

export const hopDongSearchColumns = [
  { field: 'ma_hd', headerName: 'Mã hợp đồng', width: 150 },
  { field: 'ten_hd', headerName: 'Tên hợp đồng', width: 250 }
];

export const objectListSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => {
      return <Checkbox checked={params.row.customer_code} />;
    }
  },
  { field: 'customer_code', headerName: 'Mã', width: 150 },
  { field: 'customer_name', headerName: 'Tên', width: 250 }
];

export const groupColumns = [
  {
    field: 'ma_nhom',
    headerName: 'Mã nhóm',
    width: 150
  },
  {
    field: 'ten_phan_nhom',
    headerName: 'Tên nhóm',
    width: 250
  }
];

export const congCuDungCuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cc', headerName: 'Mã công cụ', flex: 1 },
  { field: 'ten_cc', headerName: 'Tên công cụ', flex: 1 }
];

export const congCuDungCu1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: params => (
      <div className='flex h-full items-center justify-center'>
        <Checkbox onChange={e => {}} />
      </div>
    )
  },
  { field: 'ma_cc', headerName: 'Mã công cụ', flex: 1 },
  { field: 'ten_cc', headerName: 'Tên công cụ', flex: 1 }
];

export const loaiCongCuSearchColumns = [
  { field: 'ma_lts', headerName: 'Loại công cụ', flex: 1 },
  { field: 'ten_lts', headerName: 'Tên loại công cụ', flex: 1 }
];
export const donVi1SearchColumns: ExtendedGridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50 },
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 100 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 200 },
  { field: 'id', headerName: 'ID', width: 80 }
];

export const loaiYeuToSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai', headerName: 'Mã loại yếu tố', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại yếu tố', width: 250 }
];

export const loaiYeuTo1SearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'ma_loai', headerName: 'Mã loại yếu tố', width: 120 },
  { field: 'ten_loai', headerName: 'Tên loại yếu tố', width: 250 }
];

export const loaiTaiSanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lts', headerName: 'Loại tài sản', width: 150 },
  { field: 'ten_lts', headerName: 'Tên loại tài sản', width: 250 }
];

export const supplierSearchColumns: ExtendedGridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 100 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 280 },
  { field: 'cong_no_p_thu', headerName: 'Công nợ p/thu', width: 120 },
  { field: 'cong_no_p_tra', headerName: 'Công nợ p/trả', width: 120 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 120 },
  { field: 'email', headerName: 'Email', width: 160 },
  { field: 'so_dien_thoai', headerName: 'Số điện thoại', width: 160 }
];

export const supplierGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_nhom', headerName: 'Tên nhóm', width: 280 }
];

export const chiPhiSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cp', headerName: 'Mã chi phí', width: 150 },
  { field: 'ten_cp', headerName: 'Tên chi phí', width: 250 },
  {
    field: 'loai_pb',
    headerName: 'Tiêu thức phân bổ',
    width: 150,
    renderCell: params => {
      switch (params.row.loai_pb) {
        case '1':
          return '1. Số lượng';
        case '2':
          return '2. Thể tích';
        case '3':
          return '3. Khối lượng';
        case '4':
          return '4. Giá trị';
        default:
          return 'Không xác định';
      }
    }
  }
];

export const dotThanhToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dtt', headerName: 'Mã đợt', width: 100 },
  { field: 'ten_dtt', headerName: 'Tên đợt thanh toán', width: 250 },
  { field: 'ngay_tt', headerName: 'Ngày thanh toán', width: 120 },
  { field: 'ty_le', headerName: 'Tỷ lệ(%)', width: 120 },
  { field: 'tien', headerName: 'Tiền', width: 120 }
];

export const hanThanhToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tt', headerName: 'Mã thanh toán', flex: 1, width: 150 },
  { field: 'ten_tt', headerName: 'Tên thanh toán', flex: 2, width: 200 },
  { field: 'han_tt', headerName: 'Hạn thanh toán', flex: 1, width: 150 }
];

export const kheUocSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ku', headerName: 'Mã khế ước', width: 100 },
  { field: 'ten_ku', headerName: 'Tên khế ước', width: 250 }
];

export const loaiVatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lvt', headerName: 'Loại vật tư', width: 120 },
  { field: 'ten_lvt', headerName: 'Tên loại vật tư', width: 280 }
];

export const nhomVatTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nh', headerName: 'Mã nhóm', width: 120 },
  { field: 'ten_nh', headerName: 'Tên nhóm', width: 280 }
];

export const warehouseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  {
    field: 'vi_tri_yn',
    headerName: 'Theo dõi vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

export const giaoDichSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true
  },
  {
    field: 'transactionCode',
    headerName: 'Mã giao dịch',
    width: 120
  },
  {
    field: 'transactionName',
    headerName: 'Tên giao dịch',
    width: 200
  },
  {
    field: 'documentCode',
    headerName: 'Mã chứng từ',
    width: 120
  }
];

export const loSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_lo',
    headerName: 'Mã lô',
    width: 120
  },
  {
    field: 'ten_lo',
    headerName: 'Tên lô',
    width: 250
  }
];

export const viTriSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_vi_tri',
    headerName: 'Mã vị trí',
    width: 120
  },
  {
    field: 'ten_vi_tri',
    headerName: 'Tên vị trí',
    width: 250
  }
];

export const nhomNhaCungCapSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', flex: 1 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', flex: 2 }
];

export const khuVucSearchColumns: ExtendedGridColDef[] = [
  { field: 'rg_code', headerName: 'Mã khu vực', flex: 1 },
  { field: 'rgname', headerName: 'Tên khu vực', flex: 2 }
];

export const nhanVienSearchColumns = [
  { field: 'ma_nhan_vien', headerName: 'Mã nhân viên' },
  { field: 'ho_ten_nhan_vien', headerName: 'Tên nhân viên' }
];

export const khoHangSearchColumns = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  {
    field: 'vi_tri_yn',
    headerName: 'Theo dõi vị trí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

export const exportReasonSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ly_do', headerName: 'Mã lý do', width: 150 },
  { field: 'ten_ly_do', headerName: 'Tên lý do', width: 250 }
];

export const paymentInstallmentSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dtt', headerName: 'Mã đợt TT', width: 150 },
  { field: 'ten_dtt', headerName: 'Tên đợt TT', width: 250 }
];

export const invalidExpenseSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_chi_phi', headerName: 'Mã chi phí', width: 150 },
  { field: 'ten_chi_phi', headerName: 'Tên chi phí', width: 250 }
];

export const lyDoTangGiamTSCDSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tg_ts', headerName: 'Mã lý do', width: 150 },
  { field: 'ten_tg_ts', headerName: 'Tên lý do', width: 250 }
];

export const lyDoTangGiamCCDCSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tg_cc', headerName: 'Mã lý do', width: 150 },
  { field: 'ten_tg_cc', headerName: 'Tên lý do', width: 250 }
];

export const bankAccountSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'name',
    headerName: 'Tài khoản',
    width: 120
  },
  {
    field: 'ten_ngan_hang',
    headerName: 'Tên ngân hàng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <span>{params.row.ma_ngan_hang_data?.ten_ngan_hang || ''}</span>
  },
  {
    field: 'ma_ngan_hang',
    headerName: 'Mã ngân hàng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => <span>{params.row.ma_ngan_hang_data?.ma_ngan_hang || ''}</span>
  },
  { field: 'chu_tk', headerName: 'Tên tài khoản', width: 200 },
  {
    field: 'tk',
    headerName: 'Tên khoản kế toán',
    width: 200,
    renderCell: (params: GridRenderCellParams) => <span>{params.row.account_model_data?.code || ''}</span>
  },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 }
];

export const userSearchColumns: ExtendedGridColDef[] = [
  { field: 'username', headerName: 'Tên', width: 150 },
  { field: 'first_name', headerName: 'Tên đầy đủ', width: 250 },
  { field: 'is_active', headerName: 'Người sử dụng', width: 250, checkboxSelection: true }
];

export const user2SearchColumns: ExtendedGridColDef[] = [
  { field: 'username', headerName: 'Tên', width: 150 },
  {
    field: 'first_name',
    headerName: 'Tên đầy đủ',
    width: 250,
    renderCell: (params: any) => {
      const fullName = params.row?.first_name + ' ' + params.row?.last_name;
      if (fullName) return fullName;
      return params.row.username;
    }
  },
  { field: 'ct', headerName: 'Cấp trên', width: 250 },
  { field: 'id', headerName: 'ID', width: 150 }
];

export const diaChiSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_dc',
    headerName: 'Địa chỉ',
    width: 150
  },
  { field: 'ten_dc', headerName: 'Diễn giải', width: 250 }
];

export const phuongThucVanChuyenSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptvc', headerName: 'Mã phương tiện', width: 100 },
  { field: 'ten_ptvc', headerName: 'Tên phương tiện', width: 250 }
];

export const phuongThucGiaoHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ptgh', headerName: 'Mã phương thức', width: 100 },
  { field: 'ten_ptgh', headerName: 'Tên phương thức giao hàng', width: 250 }
];

export const taiKhoanNganHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'tknh', headerName: 'Tài khoản', width: 120 },
  {
    field: 'name',
    headerName: 'Tên ngân hàng',
    width: 250
  },
  {
    field: 'ma_ngan_hang',
    headerName: 'Mã ngân hàng',
    width: 120,
    renderCell: (params: any) => params.row.ma_ngan_hang_data?.ma_ngan_hang
  },
  { field: 'chu_tk', headerName: 'Tên tài khoản', width: 250 },
  { field: 'account_code', headerName: 'Tài khoản kế toán', width: 120 },
  { field: 'unit_id', headerName: 'Đơn vị', width: 120, renderCell: (params: any) => params.row.unit_id_data?.name }
];

export const hinhThucThanhToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_httt', headerName: 'Mã hình thức', width: 150 },
  { field: 'ten_httt', headerName: 'Tên hình thức', width: 250 },
  { field: 'unit_id', headerName: 'Đơn vị', width: 150 }
];

export const tyGiaQuyDoiNgoaiTeSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'ma_nt',
    headerName: 'Mã ngoại tệ',
    width: 150,
    renderCell: (params: any) => {
      return params.row.ma_nt_data?.ma_nt || '';
    }
  },
  {
    field: 'ten_nt',
    headerName: 'Tên ngoại tệ',
    width: 250,
    renderCell: (params: any) => {
      return params.row.ma_nt_data?.ten_nt || '';
    }
  },
  {
    field: 'ty_gia',
    headerName: 'Tỷ giá',
    width: 150,
    renderCell: (params: any) => {
      return params.row.ty_gia || '';
    }
  }
];

export const mauChungTuSearchColumns = [
  { field: 'ma_mau_ct', headerName: 'Mã mẫu', width: 150 },
  { field: 'ten_mau_ct', headerName: 'Tên mẫu', width: 250 }
];
export const nganHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ngan_hang', headerName: 'Mã ngân hàng', width: 150 },
  { field: 'ten_ngan_hang', headerName: 'Tên ngân hàng', width: 250 }
];

export const phuongTienVanChuyenSearchColumns = [
  { field: 'ma_ptvc', headerName: 'Mã phương tiện' },
  { field: 'ten_ptvc', headerName: 'Tên phương tiện' }
];

export const nguonDonSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nguondon', headerName: 'Mã nguồn đơn', width: 150 },
  { field: 'ten_nguondon', headerName: 'Tên nguồn đơn', width: 250 }
];

export const taiKhoanCuaHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cuahang', headerName: 'Mã cửa hàng', width: 150 },
  { field: 'ten_cuahang', headerName: 'Tên cửa hàng', width: 250 },
  {
    field: 'tk_cn',
    headerName: 'Tài khoản công nợ',
    width: 250,
    renderCell: (params: any) => {
      return params.row.tk_cn_data?.code || '';
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 150,
    renderCell: (params: any) => {
      return params.row.ma_bp_data?.ma_bp || '';
    }
  },
  {
    field: 'ten_bp',
    headerName: 'Tên bộ phận',
    width: 250,
    renderCell: (params: any) => {
      return params.row.ma_bp_data?.ten_bp || '';
    }
  }
];

export const regionColumns = khuVucSearchColumns;

export const customerSearchColumns = khachHangSearchColumns;
export const locationSearchColumns = viTriSearchColumns;
export const regionSearchColumns = khuVucSearchColumns;

export const phuongThucThanhToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_pttt', headerName: 'Mã phương thức', width: 150 },
  { field: 'ten_pttt', headerName: 'Tên phương thức', width: 250 }
];

export const diaChiNhanHangSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dcnh', headerName: 'Địa chỉ', width: 150 },
  { field: 'ten_dcnh', headerName: 'Diễn giải', width: 250 }
];

export const quyCachSearchColumns: ExtendedGridColDef[] = [
  { field: 'code_specs', headerName: 'Quy cách', width: 150 },
  { field: 'text', headerName: 'Diễn giải', width: 250 }
];

export const hoaDonSearchColumns: ExtendedGridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    type: 'boolean',
    renderHeader: () => <Checkbox />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.row.checkbox} />
  },
  { field: 'ID', headerName: 'ID', width: 120 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 150 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 150 },
  { field: 'so_hd', headerName: 'Số hóa đơn', width: 150 },
  { field: 'ngay_hd', headerName: 'Ngày hóa đơn', width: 150 },
  { field: 'ma_ct', headerName: 'Mã ct', width: 150 },
  { field: 'tien_tren_hd', headerName: 'Tiền trên hóa đơn nt', width: 150 },
  { field: 'da_thanh_toan', headerName: 'Đã thanh toán', width: 150 },
  { field: 'tien_con_phai_tt', headerName: 'Còn phải thanh toán', width: 150 },
  { field: 'ngoai_te', headerName: 'Ngoại tệ', width: 150 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 150 }
];

export const lyDoNhapXuatSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nx', headerName: 'Mã lý do', width: 150 },
  { field: 'ten_nx', headerName: 'Tên lý do nhập xuất', width: 200 }
];

export const dichVuSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_dv', headerName: 'Mã dịch vụ', width: 150 },
  { field: 'ten_dv', headerName: 'Tên dịch vụ', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 120, renderCell: (params: any) => params.row.dvt_data?.dvt },
  { field: 'tk_dt', headerName: 'Tk dịch vụ', width: 120, renderCell: (params: any) => params.row.tk_dt_data?.code },
  { field: 'tk_cp', headerName: 'Tk chi phí', width: 120, renderCell: (params: any) => params.row.tk_cp_data?.code },
  {
    field: 'ma_thue',
    headerName: 'Mã thuế',
    width: 120,
    renderCell: (params: any) => params.row.ma_thue_data?.ma_thue
  },
  { field: 'gia_nt2', headerName: 'Giá bán', width: 120 },
  { field: 'gia_nt', headerName: 'Giá mua', width: 120 }
];

export const tinhThanhSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_tinh', headerName: 'Mã tỉnh/thành', flex: 1 },
  { field: 'ten_tinh', headerName: 'Tên tỉnh/thành', flex: 2 }
];

export const accountantSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_ngvkt', headerName: 'Mã nghiệp vụ', width: 120 },
  { field: 'ten_ngvkt', headerName: 'Tên nghiệp vụ', width: 250 },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 100,
    renderCell: params => params.row.tk_no_data?.code
  },
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 100,
    renderCell: params => params.row.tk_co_data?.code
  },

  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 100,
    renderCell: params => params.row.ma_bp_data?.code
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vv
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku
  },
  {
    field: 'ma_phi',
    headerName: 'Mã phí',
    width: 120,
    renderCell: params => params.row.ma_phi_data?.ma_phi
  }
];

export const mauSoHoaDonSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_mau_so', headerName: 'Mã mẫu chứng từ', width: 120 },
  { field: 'ten_mau_so', headerName: 'Tên mẫu chứng từ', width: 250 }
];

export const chiPhiKhongHopLeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_cpkhl', headerName: 'Mã chi phí', width: 120 },
  { field: 'ten_cpkhl', headerName: 'Tên chi phí', width: 250 }
];

export const chietKhauSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_chiet_khau', headerName: 'Mã chiết khấu', width: 150 },
  { field: 'ten_chiet_khau', headerName: 'Tên chiết khấu', width: 250 }
];

export const khoangTgSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_khoang', headerName: 'Mã khoảng', width: 120 },
  { field: 'ten_khoang_thoi_gian', headerName: 'Tên khoảng thời gian', width: 200 },
  { field: 'nam', headerName: 'Năm', width: 80 },
  { field: 'loai_tg', headerName: 'Loại TG', width: 100 }
];
export const loaiCkSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_loai', headerName: 'Mã loại', width: 120 },
  { field: 'ten_loai_chiet_khau', headerName: 'Tên loại chiết khấu', width: 250 },
  { field: 'phan_loai_xu_ly', headerName: 'Phân loại xử lý', width: 180 }
];

export const taiKhoanHDDTSearchColumns = [
  { field: 'ma_tkhddt', headerName: 'Mã tài khoản', width: 150 },
  { field: 'ten_tkhddt', headerName: 'Tên tài khoản', width: 250 }
];

export const chiTieuNganSachSearchColumns = [
  { field: 'ma_ctns', headerName: 'Mã chỉ tiêu', width: 150 },
  { field: 'ten_ctns', headerName: 'Tên chỉ tiêu', width: 250 }
];

export const chungTuKeToanSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 150 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 150 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 100, renderCell: (params: any) => params.row.ma_nt_data?.ma_nt },
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 150, renderCell: (params: any) => params.row.ma_ct_data?.ma_ct },
  {
    field: 'ten_ct',
    headerName: 'Tên chứng từ',
    width: 200,
    renderCell: (params: any) => params.row.ma_ct_data?.ten_ct
  },
  { field: 't_ps_no_nt', headerName: 'Tổng phát sinh', width: 180 }
];

export const phiNganHangSearchColumns = [
  { field: 'ma_cpnh', headerName: 'Mã phí ngân hàng', width: 150 },
  { field: 'ten_cpnh', headerName: 'Tên phí ngân hàng', width: 250 },
  { field: 'tk_cpnh', headerName: 'Tài khoản phí', width: 250 },
  { field: 'ma_thue', headerName: 'Thuế suất', width: 250 }
];

export const hoaDonChungTuSearchColumns: ExtendedGridColDef[] = [
  { field: 'id', headerName: 'ID', width: 80 },
  { field: 'so_ct', headerName: 'Số c/từ', width: 180 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    renderCell: (params: any) => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: (params: any) => params.row.ma_kh_data?.customer_name
  },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120, renderCell: (params: any) => params.row.ma_nt_data?.ma_nt },
  {
    field: 'tien_nt',
    headerName: 'Tổng tiền',
    width: 150,
    renderCell: (params: any) => params.row.tien_nt || params.row.tien_nt2
  }
];
