import { useState, useCallback } from 'react';
import { CreateReceiptFormState } from './useCreateReceiptFormState';
import { CreateReceiptFormValues } from '../schema';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface CreateReceiptPayload {
  invoice_ids: string[];
  common_receipt_data: {
    ma_ngv: string;
    tk?: string;
    ngay_ct: string;
    [key: string]: any;
  };
}

interface UseCreateReceiptReturn {
  isLoading: boolean;
  createReceipt: (
    invoiceIds: string[],
    state: CreateReceiptFormState,
    formData: CreateReceiptFormValues
  ) => Promise<any>;
}

export const useCreateReceipt = (): UseCreateReceiptReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { entity } = useAuth();

  const createReceipt = useCallback(
    async (invoiceIds: string[], state: CreateReceiptFormState, formData: CreateReceiptFormValues): Promise<any> => {
      if (!entity?.slug) throw new Error('Entity slug is required');
      if (!invoiceIds.length) throw new Error('No invoices selected');
      if (!state.taiKhoan?.uuid) throw new Error('Vui lòng chọn tài khoản nợ');

      setIsLoading(true);
      try {
        // Chuyển đổi form data thành format API yêu cầu
        const payload: CreateReceiptPayload = {
          invoice_ids: invoiceIds,
          common_receipt_data: {
            ma_ngv: '1',
            tk: state.taiKhoan?.uuid, // Chỉ gửi UUID, không fallback về code
            ngay_ct: formData.ngay_ct,
            // Thêm các field khác từ form và state
            ma_ct: state.chungTu?.uuid,
            ma_nk: state.quyenChungTu?.uuid,
            tknh: state.taiKhoanNganHang?.uuid,
            loai_pt: formData.loai_pt,
            unit_id: formData.unit_id,
            ngay_hd_yn: formData.ngay_hd_yn,
            view_rs: formData.view_rs
          }
        };

        const response = await api.post(`/entities/${entity.slug}/erp/tien-mat/tao-phieu-thu-tu-hoa-don/`, payload);

        console.log('Receipt created successfully:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('Error creating receipt:', error);
        if (error.response) {
          console.error('Error response:', error.response.data);
          throw new Error(error.response.data?.message || 'Có lỗi xảy ra khi tạo phiếu thu');
        }
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    isLoading,
    createReceipt
  };
};
