import React, { useState } from 'react';
import { QUERY_KEYS, accountSearchColumns, loSearchColumns, viTriSearchColumns } from '@/constants';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField, SearchField } from '@/components/custom/arito';
import SaveTemplateDialog from '../SaveTemplateDialog';
import { Label } from '@/components/ui/label';

interface Props {
  searchFieldStates?: any;
}

const OtherTab: React.FC<Props> = ({ searchFieldStates }) => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = () => {
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = () => {
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <SearchField
            type='text'
            searchEndpoint='/'
            searchColumns={[]}
            dialogTitle='Danh mục giao dịch'
            columnDisplay='ma_gd'
            displayRelatedField='ten_gd'
            value={searchFieldStates?.transaction?.ma_gd || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setTransaction(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[300px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={searchFieldStates?.itemAccount?.code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setItemAccount(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={searchFieldStates?.revenueAccount?.code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setRevenueAccount(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            searchColumns={accountSearchColumns}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            value={searchFieldStates?.costAccount?.code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setCostAccount(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.LO}/`}
            searchColumns={loSearchColumns}
            dialogTitle='Danh mục lô hàng'
            columnDisplay='ma_lo'
            displayRelatedField='ten_lo'
            value={searchFieldStates?.batch?.ma_lo || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setBatch(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
            searchColumns={viTriSearchColumns}
            dialogTitle='Danh mục vị trí'
            columnDisplay='ma_vi_tri'
            displayRelatedField='ten_vi_tri'
            value={searchFieldStates?.location?.ma_vi_tri || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setLocation(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='dien_giai' type='text' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='flex-1'>
              <FormField
                name='report_filtering'
                type='select'
                options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => {}
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => {}
                  }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
          <div className='flex flex-1 items-center gap-1'>
            <div className='flex-1'>
              <FormField
                name='data_analysis_struct'
                type='select'
                options={[{ value: 'no_analysis', label: 'Không phân tích' }]}
              />
            </div>
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => {}
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => {}
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
};

export default OtherTab;
