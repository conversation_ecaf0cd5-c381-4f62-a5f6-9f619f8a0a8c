import { useState } from 'react';

export function useSearchFieldStates() {
  // Basic Info Tab states
  const [warehouse, setWarehouse] = useState<any>(null);

  // Detail Tab states
  const [material, setMaterial] = useState<any>(null);
  const [lot, setLot] = useState<any>(null);
  const [location, setLocation] = useState<any>(null);
  const [materialType, setMaterialType] = useState<any>(null);
  const [materialGroup1, setMaterialGroup1] = useState<any>(null);
  const [materialGroup2, setMaterialGroup2] = useState<any>(null);
  const [materialGroup3, setMaterialGroup3] = useState<any>(null);

  // Other Tab states
  const [unit, setUnit] = useState<any>(null);
  const [units, setUnits] = useState<any[]>([]);

  const addUnit = (unitToAdd: any) => {
    if (unitToAdd && !units.find(u => u.uuid === unitToAdd.uuid)) {
      setUnits(prev => [...prev, unitToAdd]);
    }
  };

  const removeUnit = (unitUuid: string) => {
    setUnits(prev => prev.filter(u => u.uuid !== unitUuid));
  };

  const getSearchFieldData = () => {
    return {
      ma_kho: warehouse?.uuid || null,

      ma_vt: material?.uuid || null,
      ma_lo: lot?.uuid || null,
      ma_vi_tri: location?.uuid || null,
      nh_vt1: materialGroup1?.uuid || null,
      nh_vt2: materialGroup2?.uuid || null,
      nh_vt3: materialGroup3?.uuid || null,

      dvt: units.length > 0 ? units.map(u => u.uuid) : unit?.uuid ? [unit.uuid] : []
    };
  };

  return {
    warehouse,
    setWarehouse,

    material,
    setMaterial,
    lot,
    setLot,
    location,
    setLocation,
    materialType,
    setMaterialType,
    materialGroup1,
    setMaterialGroup1,
    materialGroup2,
    setMaterialGroup2,
    materialGroup3,
    setMaterialGroup3,

    // Other Tab
    unit,
    setUnit,
    units,
    setUnits,

    // Helper functions
    addUnit,
    removeUnit,
    getSearchFieldData
  };
}
