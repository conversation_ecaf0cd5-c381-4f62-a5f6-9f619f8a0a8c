import { useState } from 'react';
import { SearchFormValues } from '../schema';

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  searchParams: SearchFormValues;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>({});

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchFormValues) => {
    setSearchParams(values);
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    console.log('Edit print template clicked');
  };

  return {
    initialSearchDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    setShowTable
  };
}
