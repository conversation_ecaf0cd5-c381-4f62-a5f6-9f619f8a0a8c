import React from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import { generalJournalFilterSchema, GeneralJournalFilterSchema } from '../schemas';
import { MainTab } from './filter-tabs/BasicInfoTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (data: GeneralJournalFilterSchema) => void;
}

const initialSearchValues = {
  ky1: new Date().getMonth() + 1,
  ky2: new Date().getMonth() + 1,
  nam: new Date().getFullYear(),
  ma_unit: ''
};

const SearchDialog: React.FC<SearchDialogProps> = ({ open, onClose, onSearch }) => {
  const handleSubmit = (data: GeneralJournalFilterSchema) => {
    onSearch(data);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='<PERSON><PERSON>t to<PERSON> kết chuyển'
      maxWidth='md'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={generalJournalFilterSchema}
        onSubmit={handleSubmit}
        initialData={initialSearchValues}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <MainTab />
          </div>
        }
        bottomBar={<BottomBar mode='add' onSubmit={() => {}} onClose={onClose} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
      />
    </AritoDialog>
  );
};

export default SearchDialog;
