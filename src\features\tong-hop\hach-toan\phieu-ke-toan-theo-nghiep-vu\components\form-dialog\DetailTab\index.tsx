import { GridCellParams } from '@mui/x-data-grid';
import React from 'react';
import InputTableActionBar from '../../InputTableActionBar';
import { SelectedCellInfo } from '../hooks/useDetailRows';
import { InputTable } from '@/components/custom/arito';
import { getDetailColumns } from './columns';
import { FormMode } from '@/types/form';

interface CurrencyInfo {
  ma_nt: string;
  uuid: string;
}

interface DetailTabProps {
  mode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onExport: () => void;
  onPin: () => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  currencyInfo?: CurrencyInfo | null;
  exchangeRate?: number;
}

const DetailTab: React.FC<DetailTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onExport,
  onPin,
  onCellValueChange,
  currencyInfo,
  exchangeRate
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailColumns(onCellValueChange, currencyInfo, exchangeRate)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <InputTableActionBar
          mode={mode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={onExport}
          handlePin={onPin}
        />
      }
    />
  );
};

export default DetailTab;
