import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface DeleteReceiptPayload {
  phieu_thu_uuid: string;
}

interface UseDeleteReceiptReturn {
  isLoading: boolean;
  deleteReceipt: (phieuThuUuid: string) => Promise<any>;
}

export const useDeleteReceipt = (): UseDeleteReceiptReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { entity } = useAuth();

  const deleteReceipt = useCallback(
    async (phieuThuUuid: string): Promise<any> => {
      if (!entity?.slug) throw new Error('Entity slug is required');
      if (!phieuThuUuid) throw new Error('Phiếu thu UUID is required');

      setIsLoading(true);
      try {
        const payload: DeleteReceiptPayload = {
          phieu_thu_uuid: phieuThuUuid
        };

        const response = await api.post(
          `/entities/${entity.slug}/erp/tao-phieu-thu-tu-hoa-don/delete-receipt/`,
          payload
        );

        console.log('Receipt deleted successfully:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('Error deleting receipt:', error);
        if (error.response) {
          console.error('Error response:', error.response.data);
          throw new Error(error.response.data?.message || 'Có lỗi xảy ra khi xóa phiếu thu');
        }
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    isLoading,
    deleteReceipt
  };
};
