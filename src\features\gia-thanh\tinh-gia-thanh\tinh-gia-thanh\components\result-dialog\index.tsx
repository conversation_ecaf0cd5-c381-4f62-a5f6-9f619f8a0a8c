import { Button } from '@mui/material';
import React from 'react';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { AritoDialog, AritoIcon, AritoForm } from '@/components/custom/arito';
import { useTableRows, SelectedObjData } from './hooks';
import { ResultTable } from './InputTable';

interface ResultDialogProps {
  openResultDialog: boolean;
  onCloseResultDialog: () => void;
  selectedObj: SelectedObjData[];
}

const ResultDialog = ({ selectedObj, openResultDialog, onCloseResultDialog }: ResultDialogProps) => {
  const {
    rows,
    editPrint,
    selectedRowUuid,
    handleRowClick,
    handleRefresh,
    handlePin,
    handleEdit,
    handleExport,
    handleCloseEdit,
    handleCellValueChange
  } = useTableRows(selectedObj);

  console.log(selectedObj);

  return (
    <>
      {editPrint && (
        <EditPrintTemplateDialog
          open={editPrint}
          onClose={handleCloseEdit}
          onSave={data => console.log('data:', data)}
        />
      )}

      <AritoDialog
        open={openResultDialog}
        onClose={onCloseResultDialog}
        title={'Kết quả'}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={279} />}
      >
        <AritoForm
          mode='add'
          hasAritoActionBar={false}
          className='w-[900px]'
          headerFields={
            <ResultTable
              formMode={'add'}
              rows={rows}
              selectedRowUuid={selectedRowUuid}
              onRowClick={handleRowClick}
              onRefresh={handleRefresh}
              onPin={handlePin}
              onEdit={handleEdit}
              onExport={handleExport}
              onCellValueChange={handleCellValueChange}
            />
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
          bottomBar={
            <Button onClick={onCloseResultDialog} variant='outlined'>
              <AritoIcon icon={885} className='mx-1' />
              Huỷ
            </Button>
          }
        />
      </AritoDialog>
    </>
  );
};

export default ResultDialog;
