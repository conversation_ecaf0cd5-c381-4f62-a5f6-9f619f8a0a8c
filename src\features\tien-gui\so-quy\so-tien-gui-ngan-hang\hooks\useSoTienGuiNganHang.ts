import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

export interface SoTienGuiNganHangItem {
  id: string;
  [key: string]: any;
}

export interface SoTienGuiNganHangResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SoTienGuiNganHangItem[];
}

export interface UseSoTienGuiNganHangReturn {
  data: SoTienGuiNganHangItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
  submitSoTienGuiNganHang: (requestBody: any) => Promise<void>;
}

export function useSoTienGuiNganHang(searchParams: any): UseSoTienGuiNganHangReturn {
  const { entity } = useAuth();
  const [data, setData] = useState<SoTienGuiNganHangItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (searchParams: any) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      if (!searchParams || Object.keys(searchParams).length === 0) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<SoTienGuiNganHangResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.SO_TIEN_GUI_NGAN_HANG}/`,
          searchParams
        );

        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          setData(response.data.results);
        } else {
          setData([]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const submitSoTienGuiNganHang = useCallback(
    async (requestBody: any) => {
      if (!entity?.slug) {
        const error = new Error('Entity not found');
        throw error;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<SoTienGuiNganHangResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.SO_TIEN_GUI_NGAN_HANG}/`,
          requestBody
        );

        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          setData(response.data.results);
        } else {
          setData([]);
        }
      } catch (err: any) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while submitting data';
        setError(new Error(errorMessage));
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    submitSoTienGuiNganHang
  };
}
