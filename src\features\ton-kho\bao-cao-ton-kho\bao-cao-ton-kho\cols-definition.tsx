import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { formatMoney } from '@/lib/formatUtils';

export const baoCaoTonKhoColumns = (): GridColDef[] => [
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100,
    renderCell: params => {
      return params.row.isTotalRow ? null : params.value;
    }
  },
  {
    field: 'ten_kho',
    headerName: 'Tên kho',
    width: 150,
    renderCell: params => {
      return params.row.isTotalRow ? null : params.value;
    }
  },
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 100,
    renderCell: params => {
      return params.row.isTotalRow ? null : params.value;
    }
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 230,
    renderCell: params => {
      return params.row.isTotalRow ? <span className='font-bold'>{params.value}</span> : params.value;
    }
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80
  },
  {
    field: 'ton_cuoi',
    headerName: 'Tồn cuối',
    width: 120,
    type: 'number',
    renderCell: params => {
      const value = parseFloat(params.value) || 0;
      const formattedValue = formatMoney(value);
      return params.row.isTotalRow ? <span className='font-bold'>{formattedValue}</span> : formattedValue;
    }
  },
  {
    field: 'du_cuoi',
    headerName: 'Dư cuối',
    width: 120,
    type: 'number',
    renderCell: params => {
      const value = parseFloat(params.value) || 0;
      const formattedValue = formatMoney(value);
      const fontClass = params.row.isTotalRow ? 'font-bold' : '';

      return <div className={fontClass}>{formattedValue}</div>;
    }
  }
];
