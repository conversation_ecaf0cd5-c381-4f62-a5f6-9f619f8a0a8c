import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

export interface BangKeHangBanBiTraLaiItem {
  id: string;
  [key: string]: any;
}

export interface BangKeHangBanBiTraLaiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeHangBanBiTraLaiItem[];
}

export interface UseBangKeHangBanBiTraLaiReturn {
  data: BangKeHangBanBiTraLaiItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
  submitBangKeHangBanBiTraLai: (requestBody: any) => Promise<void>;
}

export function useBangKeHangBanBiTraLai(searchParams: any): UseBangKeHangBanBiTraLaiReturn {
  const { entity } = useAuth();
  const [data, setData] = useState<BangKeHangBanBiTraLaiItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(
    async (searchParams: any) => {
      if (!entity?.slug) {
        setError(new Error('Entity not found'));
        return;
      }

      if (!searchParams || Object.keys(searchParams).length === 0) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await api.post<BangKeHangBanBiTraLaiResponse>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.BANG_KE_HANG_BAN_BI_TRA_LAI}/`,
          searchParams
        );

        if (response.data && response.data.results && Array.isArray(response.data.results)) {
          setData(response.data.results);
        } else {
          setData([]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(new Error(errorMessage));
        setData([]);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  const submitBangKeHangBanBiTraLai = useCallback(
    async (requestBody: any) => {
      await fetchData(requestBody);
    },
    [fetchData]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    submitBangKeHangBanBiTraLai
  };
}
