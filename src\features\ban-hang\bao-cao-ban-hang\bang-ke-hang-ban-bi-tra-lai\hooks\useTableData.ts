import { useState, useMemo, useEffect } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { useBangKeHangBanBiTraLai } from './useBangKeHangBanBiTraLai';
import { returnedSalesReportColumns } from '../cols-definition';
import { SearchFormValues } from '../types';

export interface UseTableDataReturn {
  tables: Array<{
    name: string;
    rows: any[];
    columns: any[];
  }>;
  handleRowClick: (params: GridRowParams) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues = {}): UseTableDataReturn {
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const { data, isLoading, error, refreshData, fetchData } = useBangKeHangBanBiTraLai(searchParams);

  useEffect(() => {
    if (searchParams && Object.keys(searchParams).length > 0) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  const handleRowClick = (params: GridRowParams) => {
    const returnedSale = params.row as any;
    setSelectedRowIndex(params.row.id.toString());
  };

  const processedData = useMemo(() => {
    if (!data || data.length === 0) return [];

    const totalRow = {
      id: 'total-row-0',
      stt: '',
      ten_kh: 'Tổng cộng',
      ngay_ct: '',
      so_ct: '',
      ma_kh: '',
      ten_nvbh: '',
      dien_giai: '',
      ma_vt: '',
      ten_vt: '',
      dvt: '',
      so_luong: data.reduce((sum, item) => sum + parseFloat(item.so_luong || 0), 0),
      gia2: data.reduce((sum, item) => sum + parseFloat(item.gia2 || 0), 0),
      tien2: data.reduce((sum, item) => sum + parseFloat(item.tien2 || 0), 0),
      thue: data.reduce((sum, item) => sum + parseFloat(item.thue || 0), 0),
      ck: data.reduce((sum, item) => sum + parseFloat(item.ck || 0), 0),
      tl_ck: data.reduce((sum, item) => sum + parseFloat(item.tl_ck || 0), 0),
      gia: data.reduce((sum, item) => sum + parseFloat(item.gia || 0), 0),
      tien: data.reduce((sum, item) => sum + parseFloat(item.tien || 0), 0),
      ma_kho: '',
      ma_ct: ''
    };

    return [totalRow, ...data];
  }, [data]);

  const tables = [
    {
      name: 'Bảng kê hàng bán bị trả lại',
      rows: processedData,
      columns: returnedSalesReportColumns(
        () => {},
        () => {}
      )
    }
  ];

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
