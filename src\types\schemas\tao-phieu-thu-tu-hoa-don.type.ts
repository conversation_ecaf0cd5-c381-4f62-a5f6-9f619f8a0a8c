/**
 * TypeScript interface for TaoPhieuThuTuHoaDon (Create Receipt from Invoice) model
 *
 * This interface represents the structure for creating receipts from invoices.
 * It defines the data structure for processing invoice-to-receipt transformations.
 */

import { QuyenChungTu } from './quyen-chung-tu.type';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './khach-hang.type';
import { ChungTu } from './chung-tu.type';
import { NgoaiTe } from './ngoai-te.type';
import { ApiResponse } from '../api.type';

/**
 * Interface for TaoPhieuThuTuHoaDon (Create Receipt from Invoice) model
 */
export interface TaoPhieuThuTuHoaDon {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model (UUID)
   */
  entity_model: string;

  /**
   * Processing type - Create or Delete
   */
  xu_ly: '1' | '2';

  /**
   * Document date from
   */
  ngay_ct1: string;

  /**
   * Document date to
   */
  ngay_ct2: string;

  /**
   * Document number from
   */
  so_ct1?: string;

  /**
   * Document number to
   */
  so_ct2?: string;

  /**
   * Document type code
   */
  ma_ct?: string;

  /**
   * Customer code
   */
  ma_kh?: string;

  /**
   * Foreign currency
   */
  ma_nt?: string;

  /**
   * Document book/permission
   */
  ma_nk?: string;

  /**
   * Unit/Department ID
   */
  unit_id: string;

  /**
   * Filter by user type
   */
  user_id0: string;

  /**
   * Status of the record
   * 0: Inactive
   * 1: Active
   */
  status: number;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for TaoPhieuThuTuHoaDon API response
 */
export type TaoPhieuThuTuHoaDonResponse = ApiResponse<TaoPhieuThuTuHoaDon>;

/**
 * Interface for creating or updating TaoPhieuThuTuHoaDon
 */
export interface TaoPhieuThuTuHoaDonInput {
  /**
   * Processing type - Create or Delete
   */
  xu_ly: '1' | '2';

  /**
   * Document date from
   */
  ngay_ct1: string;

  /**
   * Document date to
   */
  ngay_ct2: string;

  /**
   * Document number from (optional)
   */
  so_ct1?: string;

  /**
   * Document number to (optional)
   */
  so_ct2?: string;

  /**
   * Document type name (optional)
   */
  ma_ct?: string;

  /**
   * Document type code (optional)
   */
  ma_nt?: string;

  /**
   * Customer code (optional)
   */
  ma_kh?: string;

  /**
   * Foreign currency (optional)
   */
  ma_nk?: string;

  /**
   * Unit/Department ID (required)
   */
  unit_id: string;

  /**
   * Filter by user type
   */
  user_id0: string;

  /**
   * Status of the record
   * 0: Inactive
   * 1: Active
   */
  status: number;

  /**
   * Reference to the entity model (UUID)
   * This is typically handled automatically by the backend
   */
  entity_model?: string;
}

/**
 * Type for TaoPhieuThuTuHoaDon list API response
 */
export type TaoPhieuThuTuHoaDonListResponse = ApiResponse<TaoPhieuThuTuHoaDon[]>;
