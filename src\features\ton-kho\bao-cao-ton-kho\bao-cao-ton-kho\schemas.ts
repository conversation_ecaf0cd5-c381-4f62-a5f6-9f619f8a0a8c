import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.string().min(1, 'Đến ngày không được để trống'),
  ma_lvt: z.string().optional().nullable(),
  ton_kho_yn: z.boolean().optional(),
  group_by: z.number().optional(),
  mau_bc: z.coerce.number().min(1, 'Mẫu báo cáo không được để trống'),

  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_ct1: new Date().toISOString().split('T')[0],
  ma_lvt: null,
  ton_kho_yn: true,
  group_by: 0,
  mau_bc: 20,
  data_analysis_struct: '0'
};

// Keep the old schema for backward compatibility
export const baoCaoTonKhoSchema = z.object({});
