'use client';

import { useState, useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { transformFormData, calculateTotals } from '../../utils';
import { FormSchema, initialFormValues } from '../../schema';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito';
import { useDetailRows, useTaxRows } from './hooks';
import { useAuth } from '@/contexts/auth-context';
import { QuyenChungTu } from '@/types/schemas';
import { BasicInfoTab } from './BasicInfoTab';
import ConfirmDialog from '../ConfirmDialog';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { useNgoaiTe } from '@/hooks';
import DetailTab from './DetailTab';
import FileTab from './FileTab';
import TaxTab from './TaxTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
}

// Component wrapper for BottomBar to handle form context
const BottomBarWrapper = ({ detailRows, exchangeRate = 1 }: { detailRows: any[]; exchangeRate?: number }) => {
  const { setValue } = useFormContext();

  // Calculate totals automatically when detail rows change
  const totals = useMemo(() => {
    return calculateTotals(detailRows, exchangeRate);
  }, [detailRows, exchangeRate]);

  // Update form values when totals change
  useEffect(() => {
    setValue('t_ps_nt', totals.totalAmountNT);
    setValue('t_ps', totals.totalAmount);
  }, [totals, setValue]);

  return <BottomBar totalAmount={totals.totalAmount} totalAmountNT={totals.totalAmountNT} />;
};

// Component to track currency changes and provide currency info
const CurrencyTracker = ({ children }: { children: (currencyInfo: any, exchangeRate: number) => React.ReactNode }) => {
  const { watch } = useFormContext();
  const { currencies } = useNgoaiTe();

  const selectedCurrencyUuid = watch('ma_nt');
  const exchangeRate = watch('ty_gia') || 1;

  const currencyInfo = useMemo(() => {
    if (!selectedCurrencyUuid || !currencies.length) return null;
    return currencies.find(c => c.uuid === selectedCurrencyUuid) || null;
  }, [selectedCurrencyUuid, currencies]);

  return <>{children(currencyInfo, Number(exchangeRate))}</>;
};

const FormDialog = ({ open, onClose, onSubmit, formMode, initialData }: FormDialogProps) => {
  const { entityUnit } = useAuth();
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);

  const [quyenChungTu, setQuyenChungTu] = useState<QuyenChungTu | null>(initialData?.ma_nk_data || null);
  const [soChungTu, setSoChungTu] = useState<string>(initialData?.so_ct || '');
  const {
    rows: detailRows,
    selectedRowUuid: selectedDetailRowUuid,
    handleRowClick: handleDetailRowClick,
    handleAddRow: handleDetailAddRow,
    handleDeleteRow: handleDetailDeleteRow,
    handleCopyRow: handleDetailCopyRow,
    handlePasteRow: handleDetailPasteRow,
    handleMoveRow: handleDetailMoveRow,
    handleCellValueChange: handleDetailCellValueChange
  } = useDetailRows(initialData?.chi_tiet_data || []);
  const {
    rows: taxRows,
    selectedRowUuid: selectedTaxRowUuid,
    handleRowClick: handleTaxRowClick,
    handleAddRow: handleTaxAddRow,
    handleDeleteRow: handleTaxDeleteRow,
    handleCopyRow: handleTaxCopyRow,
    handlePasteRow: handleTaxPasteRow,
    handleMoveRow: handleTaxMoveRow,
    handleCellValueChange: handleTaxCellValueChange
  } = useTaxRows(initialData?.thue_data || []);

  // Calculate totals for submission
  const totals = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
    }
  }, [open]);

  const handleSubmit = async (data: any) => {
    // Add calculated totals to form data
    const dataWithTotals = {
      ...data,
      t_ps_nt: totals.totalAmountNT,
      t_ps: totals.totalAmount
    };

    // Transform data for submission
    const transformedData = transformFormData(
      dataWithTotals,
      { quyenChungTu, soChungTu },
      detailRows,
      taxRows,
      entityUnit,
      formMode,
      initialData
    );

    try {
      await onSubmit(transformedData);

      setIsFormDirty(false);
    } catch (error: any) {
      console.error('Submit failed:', error);
    }
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  return (
    <>
      <AritoForm
        mode={formMode}
        hasAritoActionBar={true}
        schema={FormSchema}
        onSubmit={handleSubmit}
        onClose={handleClose}
        initialData={initialData || initialFormValues}
        title={formMode === 'add' ? 'Mới' : 'Sửa'}
        subTitle='Phiếu kế toán theo nghiệp vụ'
        className='w-full overflow-y-auto'
        headerFields={
          <AritoHeaderTabs
            tabs={[
              {
                id: 'basic-info',
                label: 'Thông tin',
                component: (
                  <div onChange={() => setIsFormDirty(true)}>
                    <BasicInfoTab
                      formMode={formMode}
                      quyenChungTu={quyenChungTu}
                      setQuyenChungTu={setQuyenChungTu}
                      soChungTu={soChungTu}
                      setSoChungTu={setSoChungTu}
                    />
                  </div>
                )
              }
            ]}
          />
        }
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: (
              <CurrencyTracker>
                {(currencyInfo, exchangeRate) => (
                  <DetailTab
                    mode={formMode}
                    rows={detailRows}
                    selectedRowUuid={selectedDetailRowUuid}
                    onRowClick={handleDetailRowClick}
                    onAddRow={handleDetailAddRow}
                    onDeleteRow={handleDetailDeleteRow}
                    onCopyRow={handleDetailCopyRow}
                    onPasteRow={handleDetailPasteRow}
                    onMoveRow={handleDetailMoveRow}
                    onExport={() => console.log('Export clicked')}
                    onPin={() => console.log('Pin clicked')}
                    onCellValueChange={handleDetailCellValueChange}
                    currencyInfo={currencyInfo}
                    exchangeRate={exchangeRate}
                  />
                )}
              </CurrencyTracker>
            )
          },
          {
            id: 'tax',
            label: 'Thuế',
            component: (
              <TaxTab
                mode={formMode}
                rows={taxRows}
                selectedRowUuid={selectedTaxRowUuid}
                onRowClick={handleTaxRowClick}
                onAddRow={handleTaxAddRow}
                onDeleteRow={handleTaxDeleteRow}
                onCopyRow={handleTaxCopyRow}
                onPasteRow={handleTaxPasteRow}
                onMoveRow={handleTaxMoveRow}
                onExport={() => console.log('Export clicked')}
                onPin={() => console.log('Pin clicked')}
                onCellValueChange={handleTaxCellValueChange}
              />
            )
          },
          {
            id: 'file',
            label: 'File đính kèm',
            component: <FileTab formMode={formMode} />
          }
        ]}
        bottomBar={
          <CurrencyTracker>
            {(currencyInfo, exchangeRate) => <BottomBarWrapper detailRows={detailRows} exchangeRate={exchangeRate} />}
          </CurrencyTracker>
        }
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default FormDialog;
