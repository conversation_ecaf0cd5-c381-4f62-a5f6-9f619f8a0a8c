import { AlertTriangle } from 'lucide-react';
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface DeleteReceiptDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  selectedCount: number;
  isLoading?: boolean;
}

/**
 * Dialog component for confirming receipt deletion
 */
const DeleteReceiptDialog: React.FC<DeleteReceiptDialogProps> = ({
  open,
  onClose,
  onConfirm,
  selectedCount,
  isLoading = false
}) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <AlertTriangle className='h-5 w-5 text-red-500' />
            <PERSON><PERSON><PERSON> nhận xóa phiếu thu
          </DialogTitle>
          <DialogDescription>
            Bạn có chắc chắn muốn xóa {selectedCount} phiếu thu đã chọn?
            <br />
            <span className='font-medium text-red-600'>Hành động này không thể hoàn tác.</span>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className='gap-2'>
          <Button type='button' variant='outline' onClick={onClose} disabled={isLoading}>
            Hủy
          </Button>
          <Button type='button' variant='destructive' onClick={onConfirm} disabled={isLoading}>
            {isLoading ? 'Đang xóa...' : 'Xóa phiếu thu'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteReceiptDialog;
