import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField, FormField } from '@/components/custom/arito';
import { accountSearchColumns, QUERY_KEYS } from '@/constants';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { useNganHang } from '@/hooks/queries';

interface BasicInfoProps {
  searchFieldStates?: any;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ searchFieldStates }: BasicInfoProps) => {
  const { nganHangs } = useNganHang();

  const nganHangOptions = nganHangs.map(item => ({
    value: item.uuid,
    label: `${item.ma_ngan_hang} - ${item.ten_ngan_hang}`
  }));

  return (
    <div className='h-fit w-[800px] min-w-[800px] overflow-y-auto'>
      <div className='space-y-2 p-4'>
        <div className='flex flex-col space-y-3'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Tài khoản</Label>
            <div className='flex items-center gap-2'>
              <SearchField<AccountModel>
                type='text'
                displayRelatedField='name'
                columnDisplay='code'
                searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
                searchColumns={accountSearchColumns}
                value={searchFieldStates?.account?.code || ''}
                onRowSelection={(row: any) => {
                  searchFieldStates?.setAccount(row);
                }}
                dialogTitle='Danh mục tài khoản'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Ngân hàng</Label>
            <div className='w-64'>
              <FormField name='tknh' type='select' options={nganHangOptions} className='w-full' />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Ngày từ/đến</Label>
            <div className='flex gap-2'>
              <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Ngày mở sổ</Label>
            <div className='w-64'>
              <FormField name='ngay_ms' type='date' className='w-full' />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
