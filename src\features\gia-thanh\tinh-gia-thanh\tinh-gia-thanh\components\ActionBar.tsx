import { Pen<PERSON><PERSON>, Trash, Copy, FileSearch, Check, FilePlus2 } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { AritoIcon } from '@/components/custom/arito';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  searchParams?: any;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({ isViewDisabled = false, onSearchClick, searchParams }) => (
  <AritoActionBar
    titleComponent={
      <div>
        <h1 className='text-xl font-bold'>Tính giá thành các bước</h1>
        <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
          <div className='mr-2 size-2 rounded-full bg-red-500' />
          Đơn vị [0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ] kỳ {
            searchParams?.period
          }{' '}
          năm {searchParams?.year}
        </span>
      </div>
    }
  >
    {onSearchClick && <AritoActionButton title='Tính các bước' icon={Check} onClick={onSearchClick} />}
  </AritoActionBar>
);

export default ActionBar;
