import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';
import { formatMoney } from '@/lib/formatUtils';

export const returnedSalesReportColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'stt',
    headerName: 'STT',
    width: 60,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      return <span className={isFirstRow ? 'font-bold' : ''}>{isFirstRow ? '' : params.value}</span>;
    }
  },
  {
    field: 'ten_kh',
    headerName: 'Tên khách hàng',
    width: 200,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      return <span className={isFirstRow ? 'font-bold' : ''}>{isFirstRow ? 'Tổng cộng' : params.value}</span>;
    }
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 120
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 150
  },
  {
    field: 'ma_kh',
    headerName: 'Mã khách hàng',
    width: 120
  },
  {
    field: 'ten_nvbh',
    headerName: 'Tên nhân viên',
    width: 150
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 200
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 80
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'gia2',
    headerName: 'Giá bán',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'tien2',
    headerName: 'Tiền bán',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'thue',
    headerName: 'Thuế',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'ck',
    headerName: 'Chiết khấu',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'tl_ck',
    headerName: 'TL CK',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'gia',
    headerName: 'Giá vốn',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'tien',
    headerName: 'Tiền nhập',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      const isFirstRow = params.api.getRowIndexRelativeToVisibleRows(params.id) === 0;
      const value = parseFloat(params.value || 0);
      return <span className={isFirstRow ? 'font-bold' : ''}>{formatMoney(value)}</span>;
    }
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100
  },
  {
    field: 'ma_ct',
    headerName: 'Mã c/từ',
    width: 120
  }
];

// Employee columns
export const employeeColumns = [
  {
    field: 'ma_nhan_vien',
    headerName: 'Mã nhân viên',
    width: 150
  },
  {
    field: 'ho_ten_nhan_vien',
    headerName: 'Tên nhân viên',
    width: 250
  }
];

// Customer columns
export const customerSearchColumns: GridColDef[] = [
  { field: 'customer_code', headerName: 'Mã đối tượng', width: 150 },
  { field: 'customer_name', headerName: 'Tên đối tượng', width: 200 },
  { field: 'CongNoPhaiThu', headerName: 'Công nợ p/thu', width: 150 },
  { field: 'CongNoPhaiTra', headerName: 'Công nợ p/trả', width: 150 },
  { field: 'tax_code', headerName: 'Mã số thuế', width: 150 },
  { field: 'email', headerName: 'Email', width: 200 },
  { field: 'phone', headerName: 'Số điện thoại', width: 150 }
];

// Customer Group columns
export const customerGroupColumns = [
  {
    field: 'customerGroupCode',
    headerName: 'Mã nhóm khách hàng',
    width: 150
  },
  {
    field: 'customerGroupName',
    headerName: 'Tên nhóm khách hàng',
    width: 250
  }
];

// Product columns
export const productColumns: GridColDef[] = [
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 250
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.dvt_data.dvt || '';
    }
  },
  {
    field: 'nhom_vt1',
    headerName: 'Nhóm 1',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      // return params.row.nhom_vt1_data.ma_nhom || '';
    }
  },
  {
    field: 'lo_yn',
    headerName: 'Theo dõi lô',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'qc_yn',
    headerName: 'Quy cách',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'hinh_anh',
    headerName: 'Hình ảnh',
    width: 120
  }
];

// Product Group columns
export const productGroupColumns = [
  {
    field: 'productGroupCode',
    headerName: 'Mã nhóm vật tư',
    width: 150
  },
  {
    field: 'productGroupName',
    headerName: 'Tên nhóm vật tư',
    width: 250
  }
];

// Warehouse columns
export const warehouseColumns: GridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 120 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 200 },
  { field: 'loai_kho', headerName: 'Loại kho', width: 120 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 200 },
  {
    field: 'tdv_yn',
    headerName: 'Theo dõi vị trí',
    width: 150,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  }
];

// Transaction columns
export const transactionColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50
  },
  {
    field: 'transactionCode',
    headerName: 'Mã giao dịch',
    width: 120
  },
  {
    field: 'transactionName',
    headerName: 'Tên giao dịch',
    width: 200
  },
  {
    field: 'documentCode',
    headerName: 'Mã chứng từ',
    width: 120
  }
];

// Batch columns
export const batchColumns: GridColDef[] = [
  {
    field: 'batchCode',
    headerName: 'Mã lô',
    width: 120
  },
  {
    field: 'batchName',
    headerName: 'Tên lô',
    width: 200
  }
];

// Location columns
export const locationColumns = [
  {
    field: 'locationCode',
    headerName: 'Mã vị trí',
    width: 120
  },
  {
    field: 'locationName',
    headerName: 'Tên vị trí',
    width: 200
  }
];

// Account columns
export const accountColumns: GridColDef[] = [
  {
    field: 'code',
    headerName: 'Mã tài khoản',
    width: 150,
    editable: false
  },
  {
    field: 'name',
    headerName: 'Tên tài khoản',
    width: 200,
    flex: 1,
    editable: false
  },
  {
    field: 'parent_account_code',
    headerName: 'Tài khoản mẹ',
    width: 150,
    editable: false,
    renderCell: (params: GridRenderCellParams) => {
      return params.row.parent_account_code_data?.code || '';
    }
  },
  {
    field: 'is_parent_account',
    headerName: 'TK sổ cái',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'tk_chi_tiet',
    headerName: 'TK chi tiết',
    width: 120,
    renderCell: (params: GridRenderCellParams) => {
      return (
        <div className='flex h-full w-full items-center justify-center'>
          <Checkbox checked={params.value === true || params.value === 1} disabled />
        </div>
      );
    }
  },
  {
    field: 'bac_tk',
    headerName: 'Bậc TK',
    width: 100,
    editable: false,
    type: 'number'
  }
];
