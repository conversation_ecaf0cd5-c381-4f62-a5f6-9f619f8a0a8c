import React from 'react';
import { AritoHeaderTabs, AritoIcon, AritoForm, BottomBar } from '@/components/custom/arito';
import { useSearchFieldStates } from '../../hooks/useSearchFieldStates';
import { useSoTienGuiNganHang } from '../../hooks/useSoTienGuiNganHang';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { searchSchema, initialValues } from '../../schema';
import { formatDateToYYYYMMDD } from '../../utils';
import { DetailsTab, OtherTab } from '../tabs';
import BasicInfo from './BasicInfo';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();
  const { submitSoTienGuiNganHang } = useSoTienGuiNganHang({});

  const handleSubmit = async (data: any) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();

    const combinedData = {
      ...data,
      ...searchFieldData,
      ngay_ct1: formatDateToYYYYMMDD(data.ngay_ct1),
      ngay_ct2: formatDateToYYYYMMDD(data.ngay_ct2),
      ngay_ms: formatDateToYYYYMMDD(data.ngay_ms),
      tknh: data.tknh || '',
      group_by: data.group_by,
      data_analysis_struct: data.data_analysis_struct === 'no_analysis' ? '' : data.data_analysis_struct
    };

    try {
      await submitSoTienGuiNganHang(combinedData);
      onSearch(combinedData);
      onClose();
    } catch (error: any) {
      if (error?.response?.data?.errors) {
        console.log('errors', error.response.data.errors);
      } else {
        console.log('errors', error);
      }
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Sổ tiền gửi ngân hàng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='h-fit w-full'
        headerFields={
          <>
            <BasicInfo searchFieldStates={searchFieldStates} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='search' onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
