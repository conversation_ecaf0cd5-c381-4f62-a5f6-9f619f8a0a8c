import React, { useState, useEffect } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { useSoTienGuiNganHang } from './useSoTienGuiNganHang';
import { SearchFormValues } from '../schema';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
}

export function useTableData(searchParams: SearchFormValues): UseTableDataReturn {
  const { data, isLoading, error, refreshData, fetchData } = useSoTienGuiNganHang(searchParams);

  useEffect(() => {
    if (searchParams && Object.keys(searchParams).length > 0) {
      fetchData(searchParams);
    }
  }, [searchParams, fetchData]);

  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: [
        { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
        { field: 'so_ct', headerName: 'Số c/từ', width: 120 },
        { field: 'ma_kh', headerName: 'Mã khách hàng', width: 150 },
        { field: 'ten_kh', headerName: 'Tên khách hàng', width: 200 },
        { field: 'dien_giai', headerName: 'Diễn giải', width: 250 },
        { field: 'tk_du', headerName: 'Tk đối ứng', width: 120 },
        { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120 },
        { field: 'ty_gia', headerName: 'Tỷ giá', width: 120 },
        { field: 'ps_no', headerName: 'Ps nợ', width: 150, type: 'number' },
        { field: 'ps_co', headerName: 'Ps có', width: 150, type: 'number' },
        { field: 'du_tt', headerName: 'Số dư', width: 150, type: 'number' },
        { field: 'ma_chung_tu', headerName: 'Mã c/từ', width: 120 },
        { field: 'ten_ngan_hang', headerName: 'Tài khoản ngân hàng', width: 200 }
      ],
      rows: data
    }
  ]);

  React.useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables];
      updatedTables[0].rows = data;
      return updatedTables;
    });
  }, [data]);

  const handleRowClick = (params: any) => {};

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData
  };
}
