import { GridCellParams } from '@mui/x-data-grid';
import React from 'react';
import { InputTable } from '@/components/custom/arito';
import { getDetailTableColumns } from './columns';
import { SelectedCellInfo } from '../hooks';
import { FormMode } from '@/types/form';
import ActionBar from './ActionBar';

interface ResultTableProps {
  formMode: FormMode;
  rows: { uuid?: string | null }[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onRefresh?: () => void;
  onPin?: () => void;
  onEdit?: () => void;
  onExport?: () => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const ResultTable: React.FC<ResultTableProps> = ({
  formMode,
  rows,
  selectedRowUuid,
  onRowClick,
  onRefresh,
  onPin,
  onEdit,
  onExport,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailTableColumns(onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <ActionBar
          formMode={formMode}
          handleRefresh={onRefresh}
          handlePin={onPin}
          hanldeEdit={onEdit}
          handleExport={onExport}
        />
      }
    />
  );
};
