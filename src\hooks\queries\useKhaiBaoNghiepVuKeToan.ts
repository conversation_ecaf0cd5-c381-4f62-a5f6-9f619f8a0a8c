import { useState, useEffect } from 'react';
import {
  KhaiBaoNghiepVuKeToan,
  KhaiBaoNghiepVuKeToanInput,
  KhaiBaoNghiepVuKeToanResponse
} from '@/types/schemas/khai-bao-nghiep-vu-ke-toan.type';
import { useAuth } from '@/contexts/auth-context';
import QUERY_KEYS from '@/constants/query-keys';
import api from '@/lib/api';

interface UseKhaiBaoNghiepVuKeToanReturn {
  khaiBaoNghiepVuKeToans: KhaiBaoNghiepVuKeToan[];
  isLoading: boolean;
  error: string | null;
  addKhaiBaoNghiepVuKeToan: (newKhaiBaoNghiepVuKeToan: KhaiBaoNghiepVuKeToanInput) => Promise<KhaiBaoNghiepVuKeToan>;
  updateKhaiBaoNghiepVuKeToan: (
    uuid: string,
    updatedKhaiBaoNghiepVuKeToan: KhaiBaoNghiepVuKeToanInput
  ) => Promise<KhaiBaoNghiepVuKeToan>;
  deleteKhaiBaoNghiepVuKeToan: (uuid: string) => Promise<void>;
  refreshKhaiBaoNghiepVuKeToans: () => Promise<void>;
  getKhaiBaoNghiepVuKeToanByCode: (code: string) => Promise<KhaiBaoNghiepVuKeToan | null>;
  getKhaiBaoNghiepVuKeToanById: (uuid: string) => Promise<KhaiBaoNghiepVuKeToan | null>;
  getActiveKhaiBaoNghiepVuKeToans: () => Promise<KhaiBaoNghiepVuKeToan[]>;
}

/**
 * Hook for managing KhaiBaoNghiepVuKeToan (Accounting Business Operation Declaration) data
 *
 * This hook provides functions to fetch, create, update, and delete business operation declarations.
 */
export const useKhaiBaoNghiepVuKeToan = (
  initialKhaiBaoNghiepVuKeToans: KhaiBaoNghiepVuKeToan[] = []
): UseKhaiBaoNghiepVuKeToanReturn => {
  const [khaiBaoNghiepVuKeToans, setKhaiBaoNghiepVuKeToans] =
    useState<KhaiBaoNghiepVuKeToan[]>(initialKhaiBaoNghiepVuKeToans);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { entity } = useAuth();

  const fetchKhaiBaoNghiepVuKeToans = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<KhaiBaoNghiepVuKeToanResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`
      );
      setKhaiBaoNghiepVuKeToans(response.data.results);
      setError(null);
    } catch (error: any) {
      console.error('Error fetching business operation declarations:', error);
      setError(error.message || 'Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setIsLoading(false);
    }
  };

  const addKhaiBaoNghiepVuKeToan = async (
    newKhaiBaoNghiepVuKeToan: KhaiBaoNghiepVuKeToanInput
  ): Promise<KhaiBaoNghiepVuKeToan> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.post<KhaiBaoNghiepVuKeToan>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`,
        newKhaiBaoNghiepVuKeToan
      );
      setKhaiBaoNghiepVuKeToans(prev => [...prev, response.data]);
      return response.data;
    } catch (error) {
      console.error('Error adding business operation declaration:', error);
      throw error;
    }
  };

  const updateKhaiBaoNghiepVuKeToan = async (
    uuid: string,
    updatedKhaiBaoNghiepVuKeToan: KhaiBaoNghiepVuKeToanInput
  ): Promise<KhaiBaoNghiepVuKeToan> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.put<KhaiBaoNghiepVuKeToan>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/${uuid}/`,
        updatedKhaiBaoNghiepVuKeToan
      );
      setKhaiBaoNghiepVuKeToans(prev => prev.map(item => (item.uuid === uuid ? response.data : item)));
      return response.data;
    } catch (error) {
      console.error('Error updating business operation declaration:', error);
      throw error;
    }
  };

  const deleteKhaiBaoNghiepVuKeToan = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/${uuid}/`);
      setKhaiBaoNghiepVuKeToans(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting business operation declaration:', error);
      throw error;
    }
  };

  const getKhaiBaoNghiepVuKeToanByCode = async (code: string): Promise<KhaiBaoNghiepVuKeToan | null> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.get<KhaiBaoNghiepVuKeToanResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`,
        {
          params: { ma_ngvkt: code }
        }
      );
      return response.data.results.length > 0 ? response.data.results[0] : null;
    } catch (error) {
      console.error('Error fetching business operation declaration by code:', error);
      return null;
    }
  };

  const getKhaiBaoNghiepVuKeToanById = async (uuid: string): Promise<KhaiBaoNghiepVuKeToan | null> => {
    if (!entity?.slug) throw new Error('Entity not found');

    try {
      const response = await api.get<KhaiBaoNghiepVuKeToan>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/${uuid}/`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching business operation declaration by ID:', error);
      return null;
    }
  };

  const getActiveKhaiBaoNghiepVuKeToans = async (): Promise<KhaiBaoNghiepVuKeToan[]> => {
    if (!entity?.slug) return [];

    try {
      const response = await api.get<KhaiBaoNghiepVuKeToanResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`,
        {
          params: { status: 1 }
        }
      );
      return response.data.results;
    } catch (error) {
      console.error('Error fetching active business operation declarations:', error);
      return [];
    }
  };

  useEffect(() => {
    fetchKhaiBaoNghiepVuKeToans();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entity?.slug]);

  return {
    khaiBaoNghiepVuKeToans,
    isLoading,
    error,
    addKhaiBaoNghiepVuKeToan,
    updateKhaiBaoNghiepVuKeToan,
    deleteKhaiBaoNghiepVuKeToan,
    refreshKhaiBaoNghiepVuKeToans: fetchKhaiBaoNghiepVuKeToans,
    getKhaiBaoNghiepVuKeToanByCode,
    getKhaiBaoNghiepVuKeToanById,
    getActiveKhaiBaoNghiepVuKeToans
  };
};
