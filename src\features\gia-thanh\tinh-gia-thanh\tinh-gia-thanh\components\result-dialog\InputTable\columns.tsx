import { GridColDef } from '@mui/x-data-grid';
import { CellField } from '@/components/custom/arito/custom-input-table/components';

export const getDetailTableColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 200,
    renderCell: params => <CellField name='ma_vt' type='text' value={params.row.ma_vt} />
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 200,
    renderCell: params => <CellField name='ten_vt' type='text' value={params.row.ten_vt} />
  },
  {
    field: 'sl_nhap_kho',
    headerName: 'Số lượng nhập kho',
    width: 200,
    renderCell: params => <CellField name='ten_vt' type='text' value={params.row.ten_vt} />
  },
  {
    field: 'tong_gia_thanh',
    headerName: 'Tổng giá thành',
    width: 200,
    renderCell: params => <CellField name='tong_gia_thanh' type='text' value={params.row.tong_gia_thanh} />
  },
  {
    field: 'gia_thanh_don_vi',
    headerName: 'Giá thành đơn vị',
    width: 200,
    renderCell: params => <CellField name='gia_thanh_don_vi' type='text' value={params.row.gia_thanh_don_vi} />
  }
];
