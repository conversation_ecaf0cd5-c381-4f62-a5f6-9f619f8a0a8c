import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  tknh: z.string().optional(),
  ngay_ct1: z.string().min(1, '<PERSON><PERSON><PERSON> từ không được để trống').optional(),
  ngay_ct2: z.string().min(1, '<PERSON><PERSON><PERSON> đến không được để trống').optional(),
  ngay_ms: z.string().optional(),
  phan_loai: z.coerce.number().optional(),
  group_by: z.string().optional(),
  mau_bc: z.coerce.number().min(1, 'Mẫu báo cáo không được để trống').optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues = {
  tknh: '',
  ngay_ct1: new Date().toISOString().split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  ngay_ms: new Date().toISOString().split('T')[0],
  phan_loai: 2,
  group_by: '0',
  mau_bc: 20,
  report_filtering: 'no',
  data_analysis_struct: 'no_analysis'
};
