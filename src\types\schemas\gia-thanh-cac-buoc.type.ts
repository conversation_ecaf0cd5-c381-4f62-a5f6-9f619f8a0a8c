/**
 * TypeScript interfaces for GiaThanhCacBuoc (Cost Accounting Steps) model
 *
 * This interface represents the structure of the GiaThanhCacBuoc model from the backend.
 * It defines the step-by-step cost accounting process with various factors and configurations.
 */

/**
 * Type for search form values
 */
export interface GiaThanhCacBuocSearchParams {
  /**
   * Month (1-12)
   */
  ky: number;

  /**
   * Year
   */
  nam: number;

  /**
   * Show all steps (true) or only step 4 (false)
   */
  show_all?: boolean;
}

/**
 * Interface for Step 1 Materials (Raw materials inventory)
 */
export interface Step1Material {
  /**
   * Material code (UUID)
   */
  ma_vt: string;

  /**
   * Material name
   */
  ten_vt: string;

  /**
   * Opening quantity
   */
  sl_ton_dau: string;

  /**
   * Opening value
   */
  gt_ton_dau: string;

  /**
   * Import quantity
   */
  sl_nhap_kho: string;

  /**
   * Import value
   */
  gt_nhap_kho: string;

  /**
   * Export quantity
   */
  sl_xuat_kho: string;

  /**
   * Export value
   */
  gt_xuat_kho: string;

  /**
   * Unit price
   */
  gia_don_vi: string;
}

/**
 * Interface for Step 2 Products (Production output)
 */
export interface Step2Product {
  /**
   * Material code (UUID)
   */
  ma_vt: string;

  /**
   * Material name
   */
  ten_vt: string;

  /**
   * Import quantity
   */
  sl_nhap_kho: string;

  /**
   * Total cost
   */
  tong_gia_thanh: string;

  /**
   * Unit cost
   */
  gia_thanh_don_vi: string;
}

/**
 * Interface for Step 3 Combined (Combined materials and products)
 */
export interface Step3Combined {
  /**
   * Material code (UUID)
   */
  ma_vt: string;

  /**
   * Material name
   */
  ten_vt: string;

  /**
   * Opening quantity
   */
  sl_ton_dau: string;

  /**
   * Opening value
   */
  gt_ton_dau: string;

  /**
   * Import quantity
   */
  sl_nhap_kho: string;

  /**
   * Import value
   */
  gt_nhap_kho: string;

  /**
   * Export quantity
   */
  sl_xuat_kho: string;

  /**
   * Export value
   */
  gt_xuat_kho: string;

  /**
   * Unit price
   */
  gia_don_vi: string;
}

/**
 * Interface for Step 4 Products (Final products with cost calculation)
 */
export interface Step4Product {
  /**
   * Material code (UUID)
   */
  ma_vt: string;

  /**
   * Material name
   */
  ten_vt: string;

  /**
   * Import quantity
   */
  sl_nhap_kho: string;

  /**
   * Total cost
   */
  tong_gia_thanh: string;

  /**
   * Unit cost
   */
  gia_thanh_don_vi: string;
}

/**
 * Main response interface for cost calculation steps
 */
export interface GiaThanhCacBuocCalculationResponse {
  /**
   * Step 1: Raw materials inventory data
   */
  step1_materials?: Step1Material[];

  /**
   * Step 2: Production output data
   */
  step2_products?: Step2Product[];

  /**
   * Step 3: Combined materials and products data
   */
  step3_combined?: Step3Combined[];

  /**
   * Step 4: Final products with cost calculation
   */
  step4_products: Step4Product[];
}
