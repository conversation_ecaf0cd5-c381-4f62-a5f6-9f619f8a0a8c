import { Button } from '@mui/material';
import React from 'react';
import { useBaoCaoSoDuTaiQuyCuaNganHang } from '../hooks/useBaoCaoSoDuTaiQuyCuaNganHang';
import { AritoHeaderTabs, AritoIcon, AritoForm } from '@/components/custom/arito';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { DetailsTab, BasicInfo, OtherTab } from './tabs';
import { searchSchema, initialValues } from '../schema';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();
  const { submitBaoCaoSoDuTaiQuyCuaNganHang } = useBaoCaoSoDuTaiQuyCuaNganHang({});

  const handleFormSubmit = async (data: any) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();
    const combinedData = {
      ...data,
      ...searchFieldData
    };

    const requestBody = {
      tk: combinedData.tk || '',
      ngay_ct: combinedData.ngay_ct || '',
      mau_bc: parseInt(combinedData.mau_bc || '20'),
      report_filtering: combinedData.report_filtering === '1' ? '' : combinedData.report_filtering || '',
      data_analysis_struct: combinedData.data_analysis_struct === '1' ? '' : combinedData.data_analysis_struct || ''
    };

    try {
      await submitBaoCaoSoDuTaiQuyCuaNganHang(requestBody);
      onSearch(requestBody);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo số dư tại quỹ của ngân hàng'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={699} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleFormSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo formMode='add' searchFieldStates={searchFieldStates} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab formMode='add' />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab formMode='add' />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
