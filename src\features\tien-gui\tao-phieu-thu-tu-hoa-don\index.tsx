'use client';

import React, { useEffect, useState } from 'react';

import { useDialogState, useTaoPhieuThuTuHoaDon, useCreateReceipt, useDeleteReceipt } from './hooks';
import { FormDialog, ActionBar, CreateReceiptDialog, DeleteReceiptDialog } from './components';
import { getDataTableColumnsCreate, getDataTableColumnsDelete } from './cols-definition';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { CreateReceiptFormState } from './hooks/useCreateReceiptFormState';
import { CreateReceiptFormValues } from './schema';

export default function TaoPhieuThuTuHoaDonPage() {
  const {
    dialogOpen,
    searchParams: formSearchParams,
    handleDialogClose,
    handleDialogOpen,
    handleFormSubmit
  } = useDialogState();

  const { isLoading, data, totalItems, currentPage, createReceiptFromInvoice, handlePageChange } =
    useTaoPhieuThuTuHoaDon();

  // State cho việc selection và tạo phiếu thu
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const [createReceiptDialogOpen, setCreateReceiptDialogOpen] = useState(false);
  const [deleteReceiptDialogOpen, setDeleteReceiptDialogOpen] = useState(false);

  // Hook cho việc tạo phiếu thu
  const { isLoading: isCreatingReceipt, createReceipt } = useCreateReceipt();

  // Hook cho việc xóa phiếu thu
  const { isLoading: isDeletingReceipt, deleteReceipt } = useDeleteReceipt();

  // Tự động gọi API khi có formSearchParams
  useEffect(() => {
    if (formSearchParams) {
      createReceiptFromInvoice(formSearchParams).catch(error => {
        console.error('Auto API call failed:', error);
      });
    }
  }, [formSearchParams, createReceiptFromInvoice]);

  const handleSearchClick = () => {
    handleDialogOpen();
  };

  const handleRefreshClick = () => {
    if (formSearchParams) {
      createReceiptFromInvoice(formSearchParams).catch(error => {
        console.error('Refresh failed:', error);
      });
    }
  };

  const handleCreateReceiptClick = () => {
    if (!formSearchParams || selectedInvoices.length === 0) {
      return;
    }
    setCreateReceiptDialogOpen(true);
  };

  const handleDeleteReceiptClick = () => {
    if (!formSearchParams || selectedInvoices.length === 0) {
      return;
    }
    setDeleteReceiptDialogOpen(true);
  };

  const handleSubmitWithReceiptCreation = async (values: any) => {
    try {
      // Chỉ gọi handleFormSubmit để lưu searchParams
      // API sẽ được gọi tự động trong useEffect
      handleFormSubmit(values);
      clearSelection(); // Clear selection when new search
    } catch (error) {
      console.error('Error creating receipt:', error);
    }
  };

  // Handler cho việc tạo phiếu thu
  const handleCreateReceiptSubmit = async (state: CreateReceiptFormState, formData: CreateReceiptFormValues) => {
    console.log('handleCreateReceiptSubmit called with state:', state);
    console.log('handleCreateReceiptSubmit called with formData:', formData);
    console.log('selectedInvoices:', selectedInvoices);
    try {
      await createReceipt(selectedInvoices, state, formData);
      setCreateReceiptDialogOpen(false);
      setSelectedInvoices([]);
      // Refresh data sau khi tạo thành công
      if (formSearchParams) {
        createReceiptFromInvoice(formSearchParams).catch(error => {
          console.error('Refresh failed:', error);
        });
      }
    } catch (error: any) {
      console.error('Error creating receipt:', error);
    }
  };

  // Handler cho việc đóng dialog tạo phiếu thu
  const handleCreateReceiptDialogClose = () => {
    setCreateReceiptDialogOpen(false);
  };

  // Handler cho việc xóa phiếu thu
  const handleDeleteReceiptConfirm = async () => {
    try {
      // Xóa từng phiếu thu được chọn
      for (const phieuThuUuid of selectedInvoices) {
        await deleteReceipt(phieuThuUuid);
      }

      setDeleteReceiptDialogOpen(false);
      setSelectedInvoices([]);

      // Refresh data sau khi xóa thành công
      if (formSearchParams) {
        createReceiptFromInvoice(formSearchParams).catch(error => {
          console.error('Refresh failed:', error);
        });
      }
    } catch (error: any) {
      console.error('Error deleting receipts:', error);
    }
  };

  // Handler cho việc đóng dialog xóa phiếu thu
  const handleDeleteReceiptDialogClose = () => {
    setDeleteReceiptDialogOpen(false);
  };

  const handleToggleSelection = (invoiceId: string) => {
    console.log('handleToggleSelection called with invoiceId:', invoiceId);
    setSelectedInvoices(prevSelected => {
      const isCurrentlySelected = prevSelected.includes(invoiceId);
      const newSelected = isCurrentlySelected
        ? prevSelected.filter(id => id !== invoiceId)
        : [...prevSelected, invoiceId];

      console.log('Updated selectedInvoices:', newSelected);
      return newSelected;
    });
  };

  const clearSelection = () => {
    setSelectedInvoices([]);
  };

  const handleCellValueChange = (rowId: string, field: string, newValue: any) => {
    if (field === 'selected') {
      handleToggleSelection(rowId);
    }
  };

  const getColumns = () => {
    if (!formSearchParams) return [];
    if (formSearchParams.xu_ly === '1') {
      return getDataTableColumnsCreate(handleCellValueChange, selectedInvoices);
    }

    return getDataTableColumnsDelete(handleCellValueChange, selectedInvoices);
  };

  // Add selected field to data - ensure it updates when selectedInvoices changes
  const dataWithSelection = React.useMemo(() => {
    return (data || []).map((row, index) => {
      const invoiceUuid = row.id; // Original UUID from backend
      return {
        ...row,
        // Preserve original invoice UUID before it gets overwritten by formatRows
        invoice_uuid: invoiceUuid, // Store the original UUID from backend
        id: index.toString(), // Use index as display ID for DataGrid
        selected: selectedInvoices.includes(invoiceUuid) // Check against original UUID
      };
    });
  }, [data, selectedInvoices]);

  const tables = [
    {
      name: 'Kết quả tìm kiếm',
      columns: getColumns(),
      rows: dataWithSelection,
      checkboxSelection: false
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {!dialogOpen && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onCreateReceiptClick={handleCreateReceiptClick}
            onDeleteReceiptClick={handleDeleteReceiptClick}
            searchParams={formSearchParams}
            totalItems={totalItems}
            selectedCount={selectedInvoices.length}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && (
              <AritoDataTables
                key={`table-${selectedInvoices.length}-${selectedInvoices.join(',')}`}
                tables={tables}
                pageSize={100}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                serverSidePagination={true}
              />
            )}
          </div>
        </>
      )}

      <FormDialog open={dialogOpen} onClose={handleDialogClose} onSubmit={handleSubmitWithReceiptCreation} />

      <CreateReceiptDialog
        open={createReceiptDialogOpen}
        onClose={handleCreateReceiptDialogClose}
        onSubmit={handleCreateReceiptSubmit}
        selectedInvoices={selectedInvoices}
        isLoading={isCreatingReceipt}
      />

      <DeleteReceiptDialog
        open={deleteReceiptDialogOpen}
        onClose={handleDeleteReceiptDialogClose}
        onConfirm={handleDeleteReceiptConfirm}
        selectedCount={selectedInvoices.length}
        isLoading={isDeletingReceipt}
      />
    </div>
  );
}
