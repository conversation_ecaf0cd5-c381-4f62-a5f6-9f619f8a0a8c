import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

export default function DetailsTab() {
  return (
    <>
      <div className='min-h-[145px] w-[800px] min-w-[800px] space-y-2 p-4'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chi tiết theo</Label>
          <FormField
            name='phan_loai'
            className='w-96'
            type='select'
            options={[
              { value: 1, label: 'Không theo chi tiết' },
              { value: 2, label: 'Tài khoản đối ứng' },
              { value: 3, label: 'Chứng từ' }
            ]}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm theo</Label>
          <FormField
            className='w-96'
            name='group_by'
            type='select'
            options={[
              { value: '0', label: 'Không nhóm' },
              { value: '1', label: '<PERSON>ân hàng' },
              { value: '2', label: '<PERSON><PERSON>y chứng từ' },
              { value: '3', label: 'Ngân hàng và ngày chứng từ' }
            ]}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo</Label>
          <FormField
            className='w-96'
            name='mau_bc'
            type='select'
            options={[
              { value: 20, label: 'Mẫu tiền chuẩn' },
              { value: 30, label: 'Mẫu ngoại tệ' }
            ]}
          />
        </div>
      </div>
    </>
  );
}
