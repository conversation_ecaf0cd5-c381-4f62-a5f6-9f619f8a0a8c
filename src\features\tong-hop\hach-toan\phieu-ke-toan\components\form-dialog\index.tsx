'use client';

import { useState, useEffect, useMemo } from 'react';
import { getFormTitle, getFormActionButtons, calculateTotals, transformFormData } from '../../utils';

import { AritoForm, AritoHeaderTabs } from '@/components/custom/arito';

import { formSchema, initialFormValues } from '../../schemas';
import { useDetailRows, useTaxRows } from './hooks';
import { useAuth } from '@/contexts/auth-context';
import { useFormFieldState } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';
import { BottomBar } from './BottomBar';
import { DetailTab } from './DetailTab';
import { TaxsTab } from './TaxsTab';

interface FormDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit?: (data: any) => void;
  onClose: () => void;
  onAdd?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onCopy?: () => void;
}

export const FormDialog = ({
  open,
  formMode,
  initialData,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy
}: FormDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('info');
  const {
    rows: detailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailRows(initialData?.chi_tiet_data || []);
  const {
    rows: taxRows,
    selectedRowUuid: taxSelectedRowUuid,
    handleRowClick: taxHandleRowClick,
    handleAddRow: taxHandleAddRow,
    handleDeleteRow: taxHandleDeleteRow,
    handleCopyRow: taxHandleCopyRow,
    handlePasteRow: taxHandlePasteRow,
    handleMoveRow: taxHandleMoveRow,
    handleCellValueChange: taxHandleCellValueChange
  } = useTaxRows(initialData?.thue_data || []);
  const { state, actions } = useFormFieldState(initialData);
  const { entityUnit } = useAuth();

  useEffect(() => {
    if (!open) {
      setIsFormDirty(false);
      setActiveTab('info');
    }
  }, [open]);

  const { t_ps_no_nt, t_ps_co_nt } = useMemo(() => {
    return calculateTotals(detailRows);
  }, [detailRows]);

  const handleSubmit = (data: any) => {
    const totals = { t_ps_no_nt, t_ps_co_nt };
    const formData = transformFormData(data, state, entityUnit, detailRows, taxRows, totals, formMode);

    console.log('formData: ', formData);

    if (formMode === 'edit') {
      onSubmit?.({ ...formData, i_so_ct: initialData?.i_so_ct, so_ct: initialData?.so_ct });
    } else {
      onSubmit?.({ ...formData, ngay_lct: new Date().toISOString().split('T')[0] });
    }
    setIsFormDirty(false);
  };

  const handleClose = () => {
    if (isFormDirty) {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const title = getFormTitle(formMode, activeTab);
  const actionButtons = getFormActionButtons(formMode, activeTab, {
    onAdd,
    onEdit,
    onDelete,
    onCopy,
    handleClose
  });

  if (!open) return null;

  return (
    <>
      <AritoForm
        mode={formMode}
        title={title}
        initialData={initialData || initialFormValues}
        actionButtons={actionButtons}
        subTitle='Phiếu kế toán'
        onSubmit={handleSubmit}
        onClose={handleClose}
        schema={formSchema}
        headerFields={
          <div onChange={() => setIsFormDirty(true)}>
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                ...(formMode === 'view'
                  ? [
                      {
                        id: 'history',
                        label: 'Lịch sử',
                        component: <HistoryTab formMode={formMode} />
                      }
                    ]
                  : [])
              ]}
              onTabChange={handleTabChange}
              defaultTabIndex={activeTab === 'info' ? 0 : activeTab === 'history' ? 1 : 0}
            />
          </div>
        }
        tabs={
          activeTab === 'info' && [
            {
              id: 'details',
              label: 'Chi tiết',
              component: (
                <DetailTab
                  formMode={formMode}
                  rows={detailRows}
                  selectedRowUuid={detailSelectedRowUuid}
                  onRowClick={detailHandleRowClick}
                  onAddRow={detailHandleAddRow}
                  onDeleteRow={detailHandleDeleteRow}
                  onCopyRow={detailHandleCopyRow}
                  onPasteRow={detailHandlePasteRow}
                  onMoveRow={detailHandleMoveRow}
                  onCellValueChange={detailHandleCellValueChange}
                />
              )
            },
            {
              id: 'tax',
              label: 'Thuế',
              component: (
                <TaxsTab
                  formMode={formMode}
                  rows={taxRows}
                  selectedRowUuid={taxSelectedRowUuid}
                  onRowClick={taxHandleRowClick}
                  onAddRow={taxHandleAddRow}
                  onDeleteRow={taxHandleDeleteRow}
                  onCopyRow={taxHandleCopyRow}
                  onPasteRow={taxHandlePasteRow}
                  onMoveRow={taxHandleMoveRow}
                  onCellValueChange={taxHandleCellValueChange}
                />
              )
            }
          ]
        }
        bottomBar={
          activeTab === 'info' && (
            <BottomBar formMode={formMode as 'view' | 'add' | 'edit'} t_ps_no_nt={t_ps_no_nt} t_ps_co_nt={t_ps_co_nt} />
          )
        }
      />
    </>
  );
};
