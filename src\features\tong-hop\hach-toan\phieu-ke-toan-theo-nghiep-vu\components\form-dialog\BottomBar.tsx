import { FormField } from '@/components/custom/arito';
import { formatMoney } from '@/lib/formatUtils';
import { Label } from '@/components/ui/label';

interface BottomBarProps {
  totalAmount?: number;
  totalAmountNT?: number;
}

export function BottomBar({ totalAmount = 0, totalAmountNT = 0 }: BottomBarProps) {
  return (
    <div className='w-full border-t bg-white px-4 py-2'>
      <div className='flex'>
        <div className='ml-4 flex flex-col'>
          <div className='flex items-center gap-4'>
            <Label className='w-32 font-medium'>Tổng tiền</Label>
            <FormField
              name='t_ps_nt'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(totalAmountNT)}
            />
            <FormField
              name='t_ps'
              type='text'
              disabled={true}
              labelClassName='mr-2 font-medium'
              inputClassName='text-right'
              value={formatMoney(totalAmount)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
