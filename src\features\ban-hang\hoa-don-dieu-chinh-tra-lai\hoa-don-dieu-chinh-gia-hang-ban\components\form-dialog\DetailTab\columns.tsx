import { GridColDef } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  thueSearchColumns,
  vatTu1SearchColumns,
  vatTuSearchColumns,
  viTriSearchColumns,
  vuViecSearchColumns,
  warehouseSearchColumns
} from '@/constants';
import {
  BoPhan,
  DotThanhToan,
  HopDong,
  KheUoc,
  KhoHang,
  Phi,
  VatTu,
  VuViec,
  ChiPhiKhongHopLeData,
  TaiKhoan,
  Tax
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailTableColumns = (
  _taxRates: any[],
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTu1SearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục vật tư'
        value={params.row.ma_vt_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vt_data', row)}
      />
    )
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => <CellField name='ten_vt' type='text' value={params.row.ma_vt_data?.ten_vt || ''} />
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => (
      <CellField
        name='dvt'
        type='select'
        value={params.row.ma_vt_data?.dvt || params.row.dvt_data?.uuid}
        options={[
          {
            value: `${params.row.ma_vt_data?.dvt || params.row.dvt_data?.uuid}`,
            label: `${params.row.ma_vt_data?.dvt_data?.ten_dvt || params.row.dvt_data?.ten_dvt}`
          }
        ]}
        onValueChange={newValue => {
          onCellValueChange(params.row.uuid, 'dvt_data', newValue);
        }}
      />
    )
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 120,
    renderCell: params => (
      <SearchField<KhoHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
        searchColumns={warehouseSearchColumns}
        columnDisplay='ma_kho'
        dialogTitle='Danh mục kho hàng'
        value={params.row.ma_kho_data?.ma_kho || params.row.ma_vt_data?.ma_kho_data?.ma_kho || ''}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_kho_data', row);
        }}
      />
    )
  },
  {
    field: 'ma_vi_tri',
    headerName: 'Mã vị trí',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
        searchColumns={viTriSearchColumns}
        columnDisplay='ma_vi_tri'
        dialogTitle='Danh mục vị trí'
        value={params.row.ma_vi_tri_data?.ma_vi_tri || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vi_tri_data', row)}
      />
    )
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 150,
    renderCell: params => (
      <CellField
        name='so_luong'
        type='number'
        placeholder='0'
        value={params.row.so_luong}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'so_luong', newValue)}
      />
    )
  },
  {
    field: 'gia_nt2',
    headerName: 'Giá bán VND',
    width: 150,
    renderCell: params => (
      <CellField
        name='gia_nt2'
        type='number'
        placeholder='0'
        value={params.row.gia_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'gia_nt2', newValue)}
      />
    )
  },
  {
    field: 'tien_nt2',
    headerName: 'Tiền VND',
    width: 186,
    renderCell: params => (
      <CellField
        name='tien_nt2'
        type='number'
        placeholder='0'
        value={params.row.tien_nt2}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt2', newValue)}
      />
    )
  },
  {
    field: 'tl_ck',
    headerName: 'Tl ck(%)',
    width: 100,
    renderCell: params => (
      <CellField
        name='tl_ck'
        type='number'
        placeholder='0'
        value={params.row.tl_ck}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tl_ck', newValue)}
      />
    )
  },
  {
    field: 'ck_nt',
    headerName: 'Ch.khấu VND',
    width: 170,
    renderCell: params => (
      <CellField
        name='ck_nt'
        type='number'
        placeholder='0'
        value={params.row.ck_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ck_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_thue',
    headerName: 'Thuế suất',
    width: 120,
    renderCell: params => (
      <SearchField<Tax>
        searchEndpoint={`/${QUERY_KEYS.THUE}/`}
        searchColumns={thueSearchColumns}
        dialogTitle='Danh mục thuế suất'
        value={params.row.ma_thue_data?.ma_thue || ''}
        onRowSelection={(row: any) => onCellValueChange?.(params.row.uuid, 'ma_thue_data', row)}
      />
    )
  },
  {
    field: 'tk_thue_no',
    headerName: 'Tk thuế',
    width: 120,
    renderCell: params => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_thue_no_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_thue_no_data', row)}
      />
    )
  },
  {
    field: 'thue_suat',
    headerName: 'Thuế suất(%)',
    width: 120,
    renderCell: params => (
      <CellField
        name='thue_suat'
        type='number'
        placeholder='0'
        value={params.row.ma_thue_data?.thue_suat}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_suat', newValue)}
      />
    )
  },
  {
    field: 'thue_nt',
    headerName: 'Thuế VND',
    width: 180,
    renderCell: params => (
      <CellField
        name='thue_nt'
        type='number'
        placeholder='0'
        value={params.row.thue_nt}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'thue_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk đối ứng',
    width: 120,
    renderCell: params => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 'tk_ck',
    headerName: 'Tk chiết khấu',
    width: 120,
    renderCell: params => (
      <SearchField<TaiKhoan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
        searchColumns={accountSearchColumns}
        columnDisplay='code'
        dialogTitle='Danh mục tài khoản'
        value={params.row.tk_ck_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_ck_data', row)}
      />
    )
  },

  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
        searchColumns={boPhanSearchColumns}
        columnDisplay='ma_bp'
        dialogTitle='Danh mục bộ phận'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: params => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: params => (
      <SearchField
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 150,
    renderCell: params => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cp_khl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  },
  {
    field: 'so_ct_hd',
    headerName: 'Số hóa đơn',
    width: 120,
    renderCell: params => <CellField name='so_ct_hd' type='text' value={params.row.so_ct_hd || ''} />
  },
  {
    field: 'line_hd',
    headerName: 'Dòng HĐ',
    width: 100,
    renderCell: params => <CellField name='line_hd' type='text' value={params.row.line_hd || ''} />
  }
];
