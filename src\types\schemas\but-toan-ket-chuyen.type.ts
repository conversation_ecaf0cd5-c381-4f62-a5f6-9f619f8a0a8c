// Types for ButToanKetChuyen (General Journal Transfer)
export interface ButToanKetChuyen {
  uuid: string;
  entity_model?: string;
  id: string;
  stt: number;
  ten_btkc: string;
  loai_bt: string;
  nhom_bt: string;
  tk_no: string;
  tk_no_data?: any;
  tk_co: string;
  tk_co_data?: any;
  kc_ct_yn?: boolean;
  ma_bp_yn?: boolean;
  ma_bp?: string;
  ma_bp_data?: any;
  ma_vv_yn?: boolean;
  ma_vv?: string;
  ma_vv_data?: any;
  ma_hd_yn?: boolean;
  ma_hd?: string;
  ma_hd_data?: any;
  ma_ku_yn?: boolean;
  ma_ku?: string;
  ma_ku_data?: any;
  ma_phi_yn?: boolean;
  ma_phi?: string;
  ma_phi_data?: any;
  ma_sp_yn?: boolean;
  ma_sp?: string;
  ma_sp_data?: any;
  ma_lsx_yn?: boolean;
  ma_lsx?: string;
  ma_kh_yn?: boolean;
  ma_kh?: string;
  ma_kh_data?: any;
  status?: string;
  created?: string;
  updated?: string;
}

export interface ButToanKetChuyenInput {
  id: string;
  stt: number;
  ten_btkc: string;
  loai_bt: string;
  nhom_bt: string;
  tk_no: string;
  tk_co: string;
  kc_ct_yn?: boolean;
  ma_bp_yn?: boolean;
  ma_bp?: string;
  ma_vv_yn?: boolean;
  ma_vv?: string;
  ma_hd_yn?: boolean;
  ma_hd?: string;
  ma_ku_yn?: boolean;
  ma_ku?: string;
  ma_phi_yn?: boolean;
  ma_phi?: string;
  ma_sp_yn?: boolean;
  ma_sp?: string;
  ma_lsx_yn?: boolean;
  ma_lsx?: string;
  ma_kh_yn?: boolean;
  ma_kh?: string;
}

export interface ButToanKetChuyenResponse {
  results: ButToanKetChuyen[];
  count: number;
  next?: string;
  previous?: string;
}

export interface ButToanKetChuyenFilterParams {
  ky1?: number;
  ky2?: number;
  nam?: number;
  ma_unit?: string;
}
