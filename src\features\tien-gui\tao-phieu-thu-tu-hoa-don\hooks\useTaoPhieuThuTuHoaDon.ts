import { useState, useCallback } from 'react';
import {
  TaoPhieuThuTuHoaDon,
  TaoPhieuThuTuHoaDonInput,
  TaoPhieuThuTuHoaDonResponse
} from '@/types/schemas/tao-phieu-thu-tu-hoa-don.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface UseTaoPhieuThuTuHoaDonReturn {
  isLoading: boolean;
  data: any[] | null;
  totalItems: number;
  currentPage: number;
  createReceiptFromInvoice: (data: TaoPhieuThuTuHoaDonInput, page?: number) => Promise<any[]>;
  handlePageChange: (page: number) => Promise<void>;
}

export const useTaoPhieuThuTuHoaDon = (): UseTaoPhieuThuTuHoaDonReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [data, setData] = useState<any[] | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [searchParams, setSearchParams] = useState<TaoPhieuThuTuHoaDonInput | null>(null);

  const { entity } = useAuth();

  const createReceiptFromInvoice = useCallback(
    async (data: TaoPhieuThuTuHoaDonInput, page: number = 0): Promise<any[]> => {
      if (!entity?.slug) throw new Error('Entity slug is required');

      setIsLoading(true);
      try {
        const requestData = {
          xu_ly: data.xu_ly,
          ngay_ct1: data.ngay_ct1,
          ngay_ct2: data.ngay_ct2,
          so_ct1: data.so_ct1 || null,
          so_ct2: data.so_ct2 || null,
          ma_ct: data.ma_ct || null,
          ma_kh: data.ma_kh || null,
          ma_nt: data.ma_nt || null,
          ma_nk: data.ma_nk || null,
          unit_id: data.unit_id,
          user_id0: data.user_id0,
          status: data.status !== undefined && data.status !== null ? data.status : 1,
          // Thêm pagination parameters để lấy 100 bản ghi mỗi trang
          page: page + 1, // API thường dùng 1-based indexing
          page_size: 100
        };

        const response = await api.post<any>(
          `/entities/${entity.slug}/erp/tien-mat/tao-phieu-thu-tu-hoa-don/filter/`,
          requestData
        );

        // Xử lý response - có thể là array hoặc object với pagination info
        let responseData: any[];
        let total: number;

        if (Array.isArray(response.data)) {
          // Nếu response là array (không có pagination info)
          responseData = response.data;
          total = response.data.length;
        } else if (response.data && typeof response.data === 'object') {
          // Nếu response có pagination info
          responseData = response.data.results || response.data.data || response.data;
          total = response.data.count || response.data.total || responseData.length;
        } else {
          responseData = [];
          total = 0;
        }

        // Lưu dữ liệu vào state
        setData(responseData);
        setTotalItems(total);
        setCurrentPage(page);
        setSearchParams(data);

        return responseData;
      } catch (error: any) {
        if (error.response) {
          console.error('Error creating receipt from invoice:', error.response.data);
        }
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const handlePageChange = useCallback(
    async (page: number) => {
      if (searchParams) {
        await createReceiptFromInvoice(searchParams, page);
      }
    },
    [searchParams, createReceiptFromInvoice]
  );

  return {
    isLoading,
    data,
    totalItems,
    currentPage,
    createReceiptFromInvoice,
    handlePageChange
  };
};
