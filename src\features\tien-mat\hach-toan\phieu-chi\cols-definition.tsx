import { GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { formatMoney } from '@/lib/formatUtils';

export const getDataTableColumns = (handleViewClick: (row: any) => void): GridColDef[] => [
  {
    field: 'status',
    headerName: 'Trạng thái',
    width: 120,
    renderCell: params => {
      const status = params.row.status;
      switch (status) {
        case '0':
          return 'Chưa ghi sổ';
        case '3':
          return 'Chờ duyệt';
        case '5':
          return 'Đã ghi sổ';
        case '6':
          return '<PERSON>ang thực hiện';
        case '7':
          return 'Hoàn thành';
        default:
          return '';
      }
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số chứng từ',
    width: 120,
    renderCell: params => (
      <button onClick={() => handleViewClick(params.row)} className='hover:text-blue-500 hover:underline'>
        {params.row.so_ct_data?.ma_ct || params.row.so_ct}
      </button>
    )
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày chứng từ',
    width: 120,
    renderCell: params => {
      if (!params.row.ngay_ct) return '';
      try {
        const date = new Date(params.row.ngay_ct);
        if (isNaN(date.getTime())) return params.row.ngay_ct;
        return format(date, 'dd/MM/yyyy');
      } catch {
        return params.row.ngay_ct || '';
      }
    }
  },
  {
    field: 'ong_ba',
    headerName: 'Người nhận',
    width: 200,
    renderCell: params => params.row.ong_ba
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 'tk',
    headerName: 'Tài khoản có',
    width: 100,
    renderCell: params => params.row.tk_data?.code
  },
  {
    field: 't_tien',
    headerName: 'Tổng tiền',
    width: 150,
    renderCell: params => <CellField name='tien_nt' type='number' value={params.row.t_tien || 0} />
  },
  {
    field: 'ma_nt',
    headerName: 'Ngoại tệ',
    width: 120,
    renderCell: params => params.row.ma_nt_data?.ma_nt
  },
  {
    field: 'ma_ngv',
    headerName: 'Loại phiếu chi',
    width: 190,
    renderCell: params => {
      const status = params.row.ma_ngv;
      switch (status) {
        case '1':
          return '1. Chi theo hóa đơn';
        case '2':
          return '2. Chi theo đối tượng';
        case '3':
          return '3. Chi khác';
        case '4':
          return '4. Gửi tiền vào ngân hàng';
        default:
          return '';
      }
    }
  }
];

export const getInputTableColumns = (): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 300,
    renderCell: params => params.row.dien_giai
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 120,
    renderCell: params => params.row.ma_kh_data?.customer_code
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: params => params.row.ma_kh_data?.customer_name
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: params => <CellField name='du_cn' value={params.row.ma_kh_data?.du_cn || 0} type='number' />
  },
  {
    field: 'tk_no',
    headerName: 'Tài khoản nợ',
    width: 120,
    renderCell: params => params.row.tk_no_data?.code
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 150,
    type: 'number',
    renderCell: params => formatMoney(params.row.tien_nt)
  },
  {
    field: 'ma_loai_hd',
    headerName: 'Loại hóa đơn',
    width: 180,
    renderCell: params => {
      const value = params.row.ma_loai_hd;
      switch (value) {
        case '0':
          return '0. Không có hoá đơn';
        case '1':
          return '1. Hoá đơn GTGT ';
        case '2':
          return '2. Hoá đơn bán hàng thông thường';
        default:
          return '';
      }
    }
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: params => params.row.ma_bp_data?.code
  },

  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: params => params.row.ma_vv_data?.ma_vu_viec || params.row.ma_vv_data?.code
  },

  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: params => params.row.ma_hd_data?.ma_hd
  },

  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 150,
    renderCell: params => params.row.ma_dtt_data?.code
  },

  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: params => params.row.ma_ku_data?.ma_ku || params.row.ma_ku_data?.code
  },

  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: params => params.row.ma_phi_data?.ma_phi || params.row.ma_phi_data?.code
  },

  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: params => params.row.ma_sp_data?.ma_vt || params.row.ma_sp_data?.code
  },

  {
    field: 'ma_cp0',
    headerName: 'Chi phí không hợp lệ',
    width: 180,
    renderCell: params => params.row.ma_cp0_data?.ma_cp0 || params.row.ma_cp0_data?.code
  }
];

export const historyColumns: GridColDef[] = [
  { field: 'username', headerName: 'Tài khoản', width: 100 },
  { field: 'nickname', headerName: 'Tên tài khoản', width: 200 },
  { field: 'action', headerName: 'Hành động', width: 100 },
  {
    field: 'datetime0',
    headerName: 'Thời gian',
    width: 150,
    renderCell: params => {
      if (!params.row.datetime0) return '';
      try {
        const date = new Date(params.row.datetime0);
        if (isNaN(date.getTime())) return params.row.datetime0;
        return format(date, 'dd/MM/yyyy HH:mm');
      } catch {
        return params.row.datetime0 || '';
      }
    }
  }
];
