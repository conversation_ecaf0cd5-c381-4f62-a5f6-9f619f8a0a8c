import React from 'react';
import { AritoDialog, FormField } from '@/components/custom/arito';
import { ButToanKetChuyen } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface KhaiBaoDialogProps {
  open: boolean;
  onClose: () => void;
  selectedItem: ButToanKetChuyen | null;
}

const KhaiBaoDialog: React.FC<KhaiBaoDialogProps> = ({ open, onClose, selectedItem }) => {
  if (!selectedItem) return null;

  const handleSubmit = () => {
    // Handle form submission if needed
    onClose();
  };

  return (
    <AritoDialog open={open} onClose={onClose} title='Khai báo bút toán kết chuyển'>
      <div>
        <div className='space-y-4 p-4'>
          {/* Stt */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'>Stt</Label>
            <div className='flex-1'>
              <FormField name='stt' type='text' value={selectedItem.stt?.toString() || ''} disabled />
            </div>
          </div>

          {/* Tên bút toán */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'>Tên bút toán</Label>
            <div className='flex-1'>
              <FormField name='ten_btkc' type='text' value={selectedItem.ten_btkc || ''} disabled />
            </div>
          </div>

          {/* Loại kết chuyển */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'>Loại kết chuyển</Label>
            <div className='flex-1'>
              <FormField name='loai_bt' type='text' value={selectedItem.loai_bt || ''} disabled />
            </div>
          </div>

          {/* Tài khoản nợ */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'>Tài khoản nợ</Label>
            <div className='flex flex-1 gap-x-2'>
              <FormField name='tk_no' type='text' value={selectedItem.tk_no || ''} disabled className='w-24' />
              <FormField
                name='tk_no_name'
                type='text'
                value={selectedItem.tk_no_data?.name || 'Xác định kết quả kinh doanh'}
                disabled
                className='flex-1'
              />
            </div>
          </div>

          {/* Tài khoản có */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'>Tài khoản có</Label>
            <div className='flex flex-1 gap-x-2'>
              <FormField name='tk_co' type='text' value={selectedItem.tk_co || ''} disabled className='w-24' />
              <FormField
                name='tk_co_name'
                type='text'
                value={selectedItem.tk_co_data?.name || 'Giá vốn hàng bán'}
                disabled
                className='flex-1'
              />
            </div>
          </div>

          {/* Chi kết chuyển các ps chi tiết */}
          <div className='flex items-center gap-x-4'>
            <Label className='min-w-[150px] text-sm font-normal'></Label>
            <div className='flex-1'>
              <label className='flex items-center gap-2'>
                <input type='checkbox' checked={selectedItem.kc_ct_yn || false} disabled className='rounded' />
                <span className='text-sm'>Chi kết chuyển các ps chi tiết</span>
              </label>
            </div>
          </div>

          {/* Khác section */}
          <div className='border-t pt-4'>
            <Label className='text-sm font-medium text-blue-600'>Khác</Label>

            <div className='mt-4 max-h-60 space-y-4 overflow-y-auto'>
              {/* Bộ phận */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Bộ phận</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_bp'
                    checked={selectedItem.ma_bp_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Vụ việc */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Vụ việc</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_vv'
                    checked={selectedItem.ma_vv_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Hợp đồng */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Hợp đồng</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_hd'
                    checked={selectedItem.ma_hd_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Khế ước */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Khế ước</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_ku'
                    checked={selectedItem.ma_ku_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Phí */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Phí</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_phi'
                    checked={selectedItem.ma_phi_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Sản phẩm */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Sản phẩm</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_sp'
                    checked={selectedItem.ma_sp_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Lệnh sản xuất */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Lệnh sản xuất</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_lsx'
                    checked={selectedItem.ma_lsx_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Khách hàng/nhà cc */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Khách hàng/nhà cc</Label>
                <div className='flex-1'>
                  <input
                    type='checkbox'
                    name='ma_kh'
                    checked={selectedItem.ma_kh_yn || false}
                    disabled
                    className='rounded'
                  />
                </div>
              </div>

              {/* Trạng thái */}
              <div className='flex items-center gap-x-4'>
                <Label className='min-w-[150px] text-sm font-normal'>Trạng thái</Label>
                <div className='flex-1'>
                  <FormField name='status' type='text' value='1. Còn sử dụng' disabled />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom buttons */}
        <div className='flex justify-end gap-2 border-t bg-gray-50 p-4'>
          <button
            type='button'
            onClick={onClose}
            className='rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50'
          >
            Đóng
          </button>
        </div>
      </div>
    </AritoDialog>
  );
};

export default KhaiBaoDialog;
