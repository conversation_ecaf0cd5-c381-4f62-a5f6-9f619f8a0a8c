import { Label } from '@radix-ui/react-label';
import React from 'react';
import { FormField, SearchField } from '@/components/custom/arito';
import { QUERY_KEYS, donViSearchColumns } from '@/constants';

interface MainTabProps {
  formMode?: 'add' | 'edit' | 'view';
}

const MainTab: React.FC<MainTabProps> = ({ formMode = 'add' }) => {
  return (
    <div className='space-y-4 p-4'>
      {/* Kỳ từ/đến */}
      <div className='flex items-center gap-x-4'>
        <Label className='min-w-[150px] text-sm font-normal'><PERSON>ỳ từ/đến</Label>
        <div className='flex flex-1 gap-x-2'>
          <FormField type='number' name='ky1' placeholder='Từ kỳ' disabled={formMode === 'view'} className='flex-1' />
          <FormField type='number' name='ky2' placeholder='<PERSON><PERSON><PERSON> kỳ' disabled={formMode === 'view'} className='flex-1' />
        </div>
      </div>

      {/* Năm */}
      <div className='flex items-center gap-x-4'>
        <Label className='min-w-[150px] text-sm font-normal'>Năm</Label>
        <div className='flex-1'>
          <FormField type='number' name='nam' disabled={formMode === 'view'} className='max-w-[200px]' />
        </div>
      </div>

      {/* Đơn vị */}
      <div className='flex items-center gap-x-4'>
        <Label className='min-w-[150px] text-sm font-normal'>Đơn vị</Label>
        <div className='flex-1'>
          <SearchField
            disabled={formMode === 'view'}
            searchEndpoint={`/${QUERY_KEYS.DON_VI}`}
            searchColumns={donViSearchColumns}
            columnDisplay='ma_unit'
            displayRelatedField='ten_unit'
            name='ma_unit'
          />
        </div>
      </div>
    </div>
  );
};

export { MainTab };
