import React, { useEffect } from 'react';
import { CreateReceiptFormState, useCreateReceiptFormState } from '../hooks/useCreateReceiptFormState';
import { createReceiptSchema, CreateReceiptFormValues, initialCreateReceiptValues } from '../schema';
import { AritoForm, AritoDialog, AritoIcon, BottomBar } from '@/components/custom/arito';
import { CreateReceiptForm } from './CreateReceiptForm';

interface CreateReceiptDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (state: CreateReceiptFormState, values: CreateReceiptFormValues) => void;
  selectedInvoices: any[];
  isLoading?: boolean;
}

const CreateReceiptDialog: React.FC<CreateReceiptDialogProps> = ({
  open,
  onClose,
  onSubmit,
  selectedInvoices,
  isLoading = false
}) => {
  const { state, actions } = useCreateReceiptFormState();

  const handleSubmit = (values: CreateReceiptFormValues) => {
    console.log('CreateReceiptDialog handleSubmit called with values:', values);
    console.log('CreateReceiptDialog state:', state);
    console.log('Form validation passed, calling onSubmit...');
    onSubmit(state, values);
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={handleClose}
      title='Thông tin chứng từ sẽ tạo'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={16} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={createReceiptSchema}
        onSubmit={handleSubmit}
        initialData={initialCreateReceiptValues}
        className='w-full min-w-[800px]'
        title='Thông tin chứng từ sẽ tạo'
        subTitle={
          selectedInvoices.length > 0 ? `Sẽ tạo phiếu thu cho ${selectedInvoices.length} hóa đơn đã chọn` : undefined
        }
        headerFields={<CreateReceiptForm state={state} actions={actions} />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='add' onClose={handleClose} />}
      />
    </AritoDialog>
  );
};

export default CreateReceiptDialog;
