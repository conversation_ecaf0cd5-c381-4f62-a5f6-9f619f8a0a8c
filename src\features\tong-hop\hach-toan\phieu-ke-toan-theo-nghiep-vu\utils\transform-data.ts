import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { QuyenChungTu } from '@/types/schemas';
import { roundToDecimals } from './calc-util';
import { MA_CHUNG_TU } from '@/constants';

/**
 * Interface for form state containing references to selected entities
 */
export interface PhieuKeToaNghiepVuFormState {
  quyenChungTu: QuyenChungTu | null;
  soChungTu: string;
}

/**
 * Transform detail rows (chi_tiet) for API submission
 * @param detailRows - Array of detail row data from the form
 * @param exchangeRate - Exchange rate to calculate ps from ps_nt
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], exchangeRate: number = 1) => {
  return detailRows.map((row: any, index: number) => {
    const ps_nt = parseFloat(row.ps_nt) || 0;
    const ps = ps_nt * exchangeRate; // Calculate ps from ps_nt and exchange rate

    return {
      line: index + 1,

      // Accounting voucher code - FIXED: Added missing ma_ngvkt field
      ma_ngvkt: row.ma_ngvkt_data?.uuid || row.ma_ngvkt || '',

      // Account information
      tk_no: row.tk_no_data?.uuid || row.ma_ngvkt_data?.tk_no_data?.uuid || row.tk_no || '',
      tk_co: row.tk_co_data?.uuid || row.ma_ngvkt_data?.tk_co_data?.uuid || row.tk_co || '',

      // Customer/Partner information
      ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
      ma_kh_co: row.ma_kh_co_data?.uuid || row.ma_kh_data?.uuid || '',

      // Document reference
      so_ct0: row.so_ct0_data?.uuid || row.so_ct0 || '',

      // Amounts
      ps_nt, // Amount in foreign currency
      ps, // Amount in base currency (calculated from ps_nt * exchange rate)

      // Exchange rate
      ty_gia2: row.ty_gia2 || 1, // Changed from ty_gia0

      // Description
      dien_giai: row.dien_giai || '', // Removed ghi_chu as it's not in the API

      // Date reference
      ngay_ct0: row.ngay_ct0 || '',

      // Department and project information
      ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
      ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
      ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
      ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
      ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
      ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
      ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
      ma_lsx: row.ma_lsx || '',
      ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || '',

      // Additional field
      id_tt: row.id_tt || 0

      // Removed invalid fields: so_ct2, ma_tc, ma_nt0, status, transfer_yn
      // These fields don't exist in ChiTietPhieuKeToaNghiepVuModel
    };
  });
};

/**
 * Transform tax rows (thong_tin_thue) for API submission
 * @param taxRows - Array of tax row data from the form
 * @returns Transformed tax rows ready for API submission
 */
export const transformTaxRows = (taxRows: any[]) => {
  return taxRows.map((row: any, index: number) => ({
    line: index + 1,
    // Document references
    so_ct0: row.so_ct0_data?.uuid || row.so_ct0 || '',
    so_ct2: row.so_ct2_data?.uuid || row.so_ct2 || '',
    ngay_ct0: row.ngay_ct0 || '',

    // Tax information
    ma_thue: row.ma_thue_data?.uuid || row.ma_thue || '',
    thue_suat: row.thue_suat || 0,
    ma_mau_ct: row.ma_mau_ct || '',
    ma_mau_bc: row.ma_mau_bc || '',
    ma_tc_thue: row.ma_tc_thue || '',

    // Customer information
    ma_kh: row.ma_kh_data?.uuid || row.ma_kh || '',
    ten_kh_thue: row.ma_kh_data?.customer_name || row.ten_kh_thue || '',
    dia_chi: row.ma_kh_data?.address || row.dia_chi || '',
    ma_so_thue: row.ma_kh_data?.tax_code || row.ma_so_thue || '',
    ten_vt_thue: row.ten_vt_thue || '',

    // Amounts
    t_tien_nt: row.t_tien_nt || 0,
    t_tien: row.t_tien || 0,
    t_thue_nt: row.t_thue_nt || 0,
    t_thue: row.t_thue || 0,

    // Tax accounts
    tk_thue_no: row.tk_thue_no_data?.uuid || row.tk_thue_no || '',
    ten_tk_thue_no: row.tk_thue_no_data?.name || '',
    tk_du: row.tk_du_data?.uuid || row.tk_du || '',
    ten_tk_du: row.tk_du_data?.name || '',

    // Additional references
    ma_kh9: row.ma_kh9_data?.uuid || row.ma_kh9 || '',
    ten_kh9: row.ma_kh9_data?.name || '',
    ma_tt: row.ma_tt_data?.uuid || row.ma_tt || '',
    ten_tt: row.ma_tt_data?.name || '',

    // Notes and additional info
    ghi_chu: row.ghi_chu || '',

    // Department and project information
    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param taxRows - Array of tax row data
 * @param entityUnit - Entity unit information
 * @param formMode - Form mode: 'add', 'edit', or 'view'
 * @param initialData - Initial data for edit mode
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: PhieuKeToaNghiepVuFormState,
  detailRows: any[] = [],
  taxRows: any[] = [],
  entityUnit: any,
  formMode: 'add' | 'edit' | 'view' | 'search' = 'add',
  initialData: any = {}
) => {
  // Get exchange rate from form data
  const exchangeRate = parseFloat(data.ty_gia) || 1;

  // Transform detail and tax rows
  const chi_tiet = transformDetailRows(detailRows, exchangeRate);
  const thong_tin_thue = transformTaxRows(taxRows);

  // Calculate totals from detail rows
  const t_ps_nt = roundToDecimals(chi_tiet.reduce((sum, row) => sum + (Number(row.ps_nt) || 0), 0));
  const t_ps = roundToDecimals(chi_tiet.reduce((sum, row) => sum + (Number(row.ps) || 0), 0));

  // Handle document number transformation based on mode
  const isEditMode = formMode === 'edit';
  const documentNumberData = isEditMode
    ? {
        ma_nk: state.quyenChungTu?.uuid || initialData.ma_nk_data?.uuid || initialData.ma_nk || '',
        so_ct: state.soChungTu || initialData.so_ct || '',
        i_so_ct:
          state.quyenChungTu && state.soChungTu !== initialData.so_ct
            ? transformDocumentNumber(
                state.quyenChungTu,
                state.soChungTu,
                MA_CHUNG_TU.TONG_HOP.PHIEU_KE_TOAN_THEO_NGHIEP_VU
              ).i_so_ct
            : initialData.i_so_ct || 0
      }
    : transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.TONG_HOP.PHIEU_KE_TOAN_THEO_NGHIEP_VU);

  // Build the base form data object
  const baseFormData: any = {
    // Basic form data
    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_ct || '', // Use same date as document date
    dien_giai: data.dien_giai || '',
    ty_gia: data.ty_gia || '1',
    status: data.status || '1',
    transfer_yn: data.transfer_yn || false,

    // Currency information
    ma_nt: data.ma_nt || '',

    // Document information from state
    ...documentNumberData,

    // Document type and unit
    ma_ngv: data.ma_ngv || '1', // Default document type
    unit_id: entityUnit?.uuid || '', // Unit/department

    // Calculated totals
    t_ps_nt,
    t_ps,

    // Progress tracking
    id_progress: data.id_progress || 0
  };

  if (detailRows && detailRows.length > 0) {
    const hasDetailData = detailRows.some(
      row =>
        row &&
        (row.ma_ngvkt ||
          row.tk_no ||
          row.tk_co ||
          row.ma_kh ||
          row.ps_nt ||
          row.ps ||
          row.dien_giai ||
          row.ma_bp ||
          row.ma_vv ||
          row.ma_hd ||
          row.ma_dtt ||
          row.ma_ku ||
          row.ma_phi ||
          row.ma_sp ||
          row.ma_cp0 ||
          row.so_ct0 ||
          row.ty_gia2 ||
          row.ngay_ct0 ||
          row.ma_lsx ||
          row.id_tt ||
          // Data object values
          row.ma_ngvkt_data ||
          row.tk_no_data ||
          row.tk_co_data ||
          row.ma_kh_data ||
          row.ma_kh_co_data ||
          row.so_ct0_data ||
          row.ma_bp_data ||
          row.ma_vv_data ||
          row.ma_hd_data ||
          row.ma_dtt_data ||
          row.ma_ku_data ||
          row.ma_phi_data ||
          row.ma_sp_data ||
          row.ma_cp0_data)
    );

    if (hasDetailData) {
      baseFormData.chi_tiet = chi_tiet;
    }
  }

  if (taxRows && taxRows.length > 0) {
    const hasTaxData = taxRows.some(
      row =>
        row &&
        (row.ma_thue ||
          row.thue_suat ||
          row.ma_kh ||
          row.t_tien_nt ||
          row.t_tien ||
          row.t_thue_nt ||
          row.t_thue ||
          row.tk_thue_no ||
          row.tk_du ||
          row.ma_kh9 ||
          row.ma_tt ||
          row.ghi_chu ||
          row.so_ct0 ||
          row.so_ct2 ||
          row.ngay_ct0 ||
          row.ma_mau_ct ||
          row.ma_mau_bc ||
          row.ma_tc_thue ||
          row.ten_kh_thue ||
          row.dia_chi ||
          row.ma_so_thue ||
          row.ten_vt_thue ||
          row.ten_tk_thue_no ||
          row.ten_tk_du ||
          row.ten_kh9 ||
          row.ten_tt ||
          row.ma_bp ||
          row.ma_vv ||
          row.ma_hd ||
          row.ma_dtt ||
          row.ma_ku ||
          row.ma_phi ||
          row.ma_sp ||
          row.ma_lsx ||
          row.ma_cp0 ||
          // Data object values
          row.ma_thue_data ||
          row.ma_kh_data ||
          row.tk_thue_no_data ||
          row.tk_du_data ||
          row.ma_kh9_data ||
          row.ma_tt_data ||
          row.so_ct0_data ||
          row.so_ct2_data ||
          row.ma_bp_data ||
          row.ma_vv_data ||
          row.ma_hd_data ||
          row.ma_dtt_data ||
          row.ma_ku_data ||
          row.ma_phi_data ||
          row.ma_sp_data ||
          row.ma_cp0_data)
    );

    if (hasTaxData) {
      baseFormData.thong_tin_thue = thong_tin_thue;
    }
  }

  return baseFormData;
};
