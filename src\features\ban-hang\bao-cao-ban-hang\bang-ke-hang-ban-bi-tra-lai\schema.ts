import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.string().min(1, '<PERSON><PERSON><PERSON> từ không được để trống'),
  ngay_ct2: z.string().min(1, '<PERSON><PERSON><PERSON> đến không được để trống'),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),

  ma_lvt: z.string().optional(),
  ton_kho_yn: z.boolean().optional(),
  mau_bc: z.coerce.number().min(1, 'Mẫu báo cáo không được để trống'),

  dien_giai: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_ct1: new Date().toISOString().split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  so_ct1: '',
  so_ct2: '',
  ma_lvt: '',
  ton_kho_yn: true,
  mau_bc: 20,
  dien_giai: '',
  data_analysis_struct: 'no_analysis'
};
