import { z } from 'zod';

export const FormSchema = z.object({
  ngay_ct: z.string().min(1, '<PERSON><PERSON><PERSON> chứng từ không được để trống'),
  ma_nt: z.string().min(1, '<PERSON>ã ngoại tệ không được để trống'),
  dien_giai: z.string().optional(),
  ty_gia: z.string().min(1, 'Tỷ giá không được để trống'),
  status: z.string().min(1, 'Trạng thái không được để trống'),
  transfer_yn: z.boolean().optional(),
  // Calculated totals (read-only)
  t_ps_nt: z.coerce.number().optional(), // Total amount in foreign currency
  t_ps: z.coerce.number().optional() // Total amount in base currency
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ngay_ct: new Date().toISOString().split('T')[0],
  ma_nt: 'VND',
  dien_giai: '',
  ty_gia: '1',
  status: '1',
  transfer_yn: false,
  t_ps_nt: 0,
  t_ps: 0
};

export const SearchFormSchema = z.object({
  ngay_ct1: z.coerce.date(),
  ngay_ct2: z.coerce.date(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  dien_giai: z.string().optional(),
  status: z.string().nonempty('Trạng thái không được để trống'),
  user_id0: z.string().optional()
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: new Date(),
  ngay_ct2: new Date(),
  so_ct1: '',
  so_ct2: '',
  dien_giai: '',
  status: 'all',
  user_id0: '0'
};
