'use client';

import React, { useState, useMemo } from 'react';
import { getClosingJournalEntryColumns, getOtherJournalEntryColumns } from './cols-definition';
import { ActionBar, SearchDialog, KhaiBaoDialog } from './components';
import { useDialogState, useButToanKetChuyen } from './hooks';
import { AritoDataTables } from '@/components/custom/arito';
import { ButToanKetChuyen } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';

export const ButToanKetChuyenPage = () => {
  const [showTable, setShowTable] = useState(false);
  const [selectedRows, setSelectedRows] = useState<ButToanKetChuyen[]>([]);
  const [tableData, setTableData] = useState<ButToanKetChuyen[]>([]);
  const [khaiBaoDialogOpen, setKhaiBaoDialogOpen] = useState(false);
  const [selectedItemForKhaiBao, setSelectedItemForKhaiBao] = useState<ButToanKetChuyen | null>(null);

  const { entity, loading: authLoading, error: authError, refetchProfile } = useAuth();

  // Handle row selection
  const handleRowSelect = (row: ButToanKetChuyen, checked: boolean) => {
    if (checked) {
      setSelectedRows(prev => [...prev, row]);
    } else {
      setSelectedRows(prev => prev.filter(item => item.uuid !== row.uuid));
    }
  };

  // Create columns with selection logic
  const mainColumns = useMemo(
    () =>
      getClosingJournalEntryColumns(
        () => {},
        () => {},
        selectedRows,
        handleRowSelect
      ),
    [selectedRows]
  );

  const otherColumns = useMemo(
    () =>
      getOtherJournalEntryColumns(
        () => {},
        () => {},
        selectedRows,
        handleRowSelect
      ),
    [selectedRows]
  );

  const { dialogOpen, showForm, searchParams, handleDialogClose, handleDialogOpen, handleFormSubmit, setShowForm } =
    useDialogState();

  const {
    butToanKetChuyens,
    isLoading,
    isSuccess,
    error,
    createdReceiptId,
    createGeneralJournal,
    getButToanKetChuyensByFilter,
    refreshButToanKetChuyens,
    ketChuyenButToan
  } = useButToanKetChuyen();

  // Show loading while auth is loading
  if (authLoading) {
    return (
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col items-center justify-center space-y-4'>
        <div className='text-lg'>Đang tải thông tin xác thực...</div>
      </div>
    );
  }

  // Show error if no entity after auth loaded
  if (!authLoading && !entity?.slug) {
    return (
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col items-center justify-center space-y-4'>
        <div className='text-lg text-red-500'>Không thể tải thông tin entity.</div>
        <div className='text-sm text-gray-600'>Auth Error: {authError?.message || 'Không có lỗi cụ thể'}</div>
        <button onClick={() => refetchProfile()} className='rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600'>
          Thử lại
        </button>
        <div className='text-xs text-gray-400'>Kiểm tra console để xem thêm thông tin debug</div>
      </div>
    );
  }

  const handleSearchClick = () => {
    handleDialogOpen();
  };

  const handleRefresh = async () => {
    await refreshButToanKetChuyens();
  };

  const handleKetChuyenClick = async () => {
    if (selectedRows.length === 0) {
      alert('Vui lòng chọn ít nhất một bút toán để kết chuyển');
      return;
    }

    try {
      await ketChuyenButToan(selectedRows);
      alert('Kết chuyển thành công!');
      // Refresh data after successful transfer
      await handleRefresh();
    } catch (error) {
      alert('Có lỗi xảy ra khi kết chuyển. Vui lòng thử lại.');
    }
  };

  const handleXoaKetChuyenClick = () => {
    // Handle xóa kết chuyển logic
  };

  const handleXemKhaiBaoClick = () => {
    if (selectedRows.length !== 1) {
      alert('Vui lòng chọn một bút toán để xem khai báo');
      return;
    }

    setSelectedItemForKhaiBao(selectedRows[0]);
    setKhaiBaoDialogOpen(true);
  };

  const handleXemKetQuaClick = () => {
    // Handle xem kết quả logic
  };

  const handleSearch = async (searchData: any) => {
    try {
      // Call the API to get filtered data
      const results = await getButToanKetChuyensByFilter(searchData);

      // Update table data
      setTableData(results);
      setSelectedRows([]);

      // Update form state
      handleFormSubmit(searchData);

      // Show the table with results
      setShowTable(true);
      handleDialogClose();
    } catch (error) {
      // Handle error silently or show user-friendly message
    }
  };
  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      {/* Search Dialog */}
      <SearchDialog open={dialogOpen} onClose={handleDialogClose} onSearch={handleSearch} />

      {/* Main Content - Only show after search */}
      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefresh}
            onKetChuyenClick={handleKetChuyenClick}
            onXoaKetChuyenClick={handleXoaKetChuyenClick}
            onXemKhaiBaoClick={handleXemKhaiBaoClick}
            onXemKetQuaClick={handleXemKetQuaClick}
          />

          <div className='flex-1 overflow-hidden'>
            <AritoDataTables
              tables={[
                {
                  name: 'Bút toán kế toán',
                  rows: tableData,
                  columns: mainColumns
                },
                {
                  name: 'Bút toán vụ việc, công trình',
                  rows: [],
                  columns: otherColumns
                }
              ]}
              pageSize={30}
              getRowId={row => row.uuid}
            />
          </div>
        </>
      )}

      {/* Khai báo Dialog */}
      <KhaiBaoDialog
        open={khaiBaoDialogOpen}
        onClose={() => {
          setKhaiBaoDialogOpen(false);
          setSelectedItemForKhaiBao(null);
        }}
        selectedItem={selectedItemForKhaiBao}
      />
    </div>
  );
};

export default ButToanKetChuyenPage;
