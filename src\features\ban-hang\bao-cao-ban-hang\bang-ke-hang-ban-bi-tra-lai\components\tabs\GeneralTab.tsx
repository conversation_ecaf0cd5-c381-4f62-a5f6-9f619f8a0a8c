import React from 'react';
import {
  QUERY_KEYS,
  customerSearchColumns,
  nhanVienSearchColumns,
  nhomNhaCungCapSearchColumns,
  khuVucSearchColumns,
  vatTuSearchColumns,
  warehouseSearchColumns,
  nhomColumns
} from '@/constants';
import { SearchField, FormField } from '@/components/custom/arito';
import { useLoaiVatTu } from '@/hooks/queries';
import { Label } from '@/components/ui/label';

interface Props {
  searchFieldStates?: any;
}

const GeneralTab: React.FC<Props> = ({ searchFieldStates }) => {
  const { loaiVatTus } = useLoaiVatTu();

  const loaiVatTuOptions = [
    ...loaiVatTus.map(item => ({
      value: item.uuid,
      label: `${item.ma_lvt} - ${item.ten_lvt}`
    }))
  ];

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
          <SearchField
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
            searchColumns={nhanVienSearchColumns}
            dialogTitle='Danh mục nhân viên'
            columnDisplay='ma_nhan_vien'
            displayRelatedField='ho_ten_nhan_vien'
            value={searchFieldStates?.employee?.ma_nhan_vien || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setEmployee(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={customerSearchColumns}
            dialogTitle='Danh mục khách hàng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            value={searchFieldStates?.customer?.customer_code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setCustomer(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex w-2/3 gap-3'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC1`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm khách hàng 1'
              columnDisplay='ma_nhom'
              value={searchFieldStates?.customerGroup1?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setCustomerGroup1(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC2`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm khách hàng 2'
              columnDisplay='ma_nhom'
              value={searchFieldStates?.customerGroup2?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setCustomerGroup2(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=NCC3`}
              searchColumns={nhomColumns}
              dialogTitle='Danh mục nhóm khách hàng'
              columnDisplay='ma_nhom'
              value={searchFieldStates?.customerGroup3?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setCustomerGroup3(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
            searchColumns={khuVucSearchColumns}
            dialogTitle='Danh mục khu vực'
            columnDisplay='rg_code'
            displayRelatedField='rgname'
            value={searchFieldStates?.region?.rg_code || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setRegion(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            searchColumns={vatTuSearchColumns}
            dialogTitle='Danh mục vật tư'
            columnDisplay='ma_vt'
            displayRelatedField='ten_vt'
            value={searchFieldStates?.product?.ma_vt || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setProduct(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại vật tư:</Label>
          <div className='flex-1'>
            <div className='flex items-center gap-4'>
              <div className='w-40'>
                <FormField name='ma_lvt' type='select' options={loaiVatTuOptions} />
              </div>
              <div className='whitespace-nowrap'>
                <FormField name='trackInventory' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
              </div>
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex w-2/3 gap-3'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT1`}
              searchColumns={nhomNhaCungCapSearchColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='ma_nhom'
              value={searchFieldStates?.productGroup1?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setProductGroup1(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT2`}
              searchColumns={nhomNhaCungCapSearchColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='ma_nhom'
              value={searchFieldStates?.productGroup2?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setProductGroup2(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT3`}
              searchColumns={nhomNhaCungCapSearchColumns}
              dialogTitle='Danh mục nhóm vật tư'
              columnDisplay='ma_nhom'
              displayRelatedField='ten_nhom'
              value={searchFieldStates?.productGroup3?.ma_nhom || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setProductGroup3(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto max-w-full'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho:</Label>
          <SearchField
            type='text'
            searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
            searchColumns={warehouseSearchColumns}
            dialogTitle='Danh mục kho hàng'
            columnDisplay='ma_kho'
            displayRelatedField='ten_kho'
            value={searchFieldStates?.warehouse?.ma_kho || ''}
            onRowSelection={(row: any) => {
              searchFieldStates?.setWarehouse(row);
            }}
            className='w-full'
            classNameRelatedField='w-auto min-w-[445px] max-w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-96'>
            <FormField
              name='mau_bc'
              type='select'
              options={[
                { value: 10, label: 'Mẫu số lượng' },
                {
                  value: 20,
                  label: 'Mẫu số lượng và giá trị'
                },
                {
                  value: 30,
                  label: 'Mẫu số lượng và giá trị ngoại tệ'
                }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
