import { useState } from 'react';
import {
  Account<PERSON><PERSON><PERSON>,
  Quy<PERSON><PERSON>hung<PERSON>u,
  HanT<PERSON><PERSON><PERSON><PERSON>,
  KhachHang,
  Nhan<PERSON>ien,
  <PERSON><PERSON>i<PERSON><PERSON>,
  Don<PERSON>i<PERSON>o<PERSON>o,
  <PERSON>a<PERSON><PERSON>,
  Phuong<PERSON><PERSON><PERSON>,
  Phuong<PERSON>hu<PERSON><PERSON><PERSON>,
  PhuongThucThanh<PERSON>oan,
  ChungT<PERSON>
} from '@/types/schemas';

export interface FormFieldState {
  // Customer information
  khachHang: KhachHang | null;
  chungTu: ChungTu | null;
  // Document information
  quyenChungTu: QuyenChungTu | null;
  ngoaiTe: NgoaiTe | null;

  // So chung tu
  soChungTu: string;
}

export interface FormFieldActions {
  // Search field setters
  setKhachHang: (khachHang: KhachHang) => void;
  setChungTu: (chungTu: ChungTu) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;
  // So chung tu
  setSoChungTu: (soChungTu: string) => void;

  // Utility functions
  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  // Customer information
  khachHang: null,
  chungTu: null,
  // Document information
  quyenChungTu: null,
  ngoaiTe: null,

  // Other information
  // So chung tu
  soChungTu: ''
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    // Customer information
    khachHang: initialData.ma_kh_data || null,
    chungTu: initialData.ma_ct_data || null,
    quyenChungTu: initialData.so_ct_data || null,
    ngoaiTe: initialData.ma_nt_data || null,
    soChungTu: initialData.so_ct || ''
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setChungTu: (chungTu: ChungTu) => {
      setState(prev => ({
        ...prev,
        chungTu
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setNgoaiTe: (ngoaiTe: NgoaiTe) => {
      setState(prev => ({
        ...prev,
        ngoaiTe
      }));
    },

    // So chung tu
    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({ ...prev, soChungTu }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
