import { useFormContext } from 'react-hook-form';
import React from 'react';
import { CreateReceiptFormState, CreateReceiptFormActions } from '../hooks/useCreateReceiptFormState';
import { taiKhoanNganHangSearchColumns, accountSearchColumns } from '@/constants/search-columns';
import { ChungTu, NgoaiTe, QuyenChungTu, TaiKhoanNganHang, AccountModel } from '@/types/schemas';
import { documentTypeSearchColumns, foreignCurrencySearchColumns } from '../cols-definition';
import { FormField, SearchField, UnitDropdown } from '@/components/custom/arito/form';
import { QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';

interface CreateReceiptFormProps {
  state: CreateReceiptFormState;
  actions: CreateReceiptFormActions;
}

export const CreateReceiptForm: React.FC<CreateReceiptFormProps> = ({ state, actions }) => {
  const { setValue } = useFormContext();

  return (
    <div className='space-y-4 p-4'>
      {/* Đơn vị */}
      <div className='flex items-center'>
        <div className='w-1/2'>
          <UnitDropdown formMode='add' className='w-full' />
        </div>
      </div>

      {/* Chứng từ */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Chứng từ</Label>
        <SearchField<ChungTu>
          name='ma_ct'
          searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
          searchColumns={documentTypeSearchColumns}
          columnDisplay='ma_ct'
          displayRelatedField='ten_ct'
          value={state.chungTu?.ma_ct}
          onRowSelection={row => {
            console.log('Chứng từ selected:', row);
            actions.setChungTu(row);
            setValue('ma_ct', row?.ma_ct || '');
          }}
        />
      </div>

      {/* Ngày chứng từ */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Ngày c/từ</Label>
        <div className='w-1/2'>
          <FormField name='ngay_ct' type='date' />
        </div>
      </div>

      {/* Quyển/Số chứng từ */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Quyển/Số ct</Label>
        <SearchField<QuyenChungTu>
          name='ma_nk'
          searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
          searchColumns={quyenChungTuSearchColumns}
          columnDisplay='ten_nk'
          displayRelatedField='ma_nk'
          value={state.quyenChungTu?.ma_nk}
          onRowSelection={row => {
            console.log('Quyển chứng từ selected:', row);
            actions.setQuyenChungTu(row);
            setValue('ma_nk', row?.ma_nk || '');
          }}
        />
      </div>

      {/* Ngân hàng */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Tài khoản ngân hàng</Label>
        <SearchField<TaiKhoanNganHang>
          name='tknh'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}`}
          searchColumns={taiKhoanNganHangSearchColumns}
          columnDisplay='account_code'
          displayRelatedField='name'
          value={state.taiKhoanNganHang?.account_code}
          onRowSelection={row => {
            console.log('Tài khoản ngân hàng selected:', row);
            actions.setTaiKhoanNganHang(row);
            setValue('tknh', row?.account_code || '');
          }}
        />
      </div>

      {/* Tài khoản nợ */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Tài khoản nợ</Label>
        <SearchField<AccountModel>
          name='tk'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          displayRelatedField='name'
          value={state.taiKhoan?.code}
          onRowSelection={row => {
            console.log('Tài khoản selected:', row);
            actions.setTaiKhoan(row);
            setValue('tk', row?.code || '');
          }}
        />
      </div>

      {/* Loại phiếu thu */}
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Loại phiếu thu</Label>
        <div className='w-1/2'>
          <FormField
            name='loai_pt'
            type='select'
            options={[
              { value: '1', label: 'Thu theo hóa đơn' },
              { value: '2', label: 'Thu theo khách hàng' }
            ]}
          />
        </div>
      </div>
      {/* Checkboxes */}
      <div className='space-y-3'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'></Label>
          <div className='w-1/2'>
            <FormField name='ngay_hd_yn' type='checkbox' label='Dùng ngày hóa đơn làm ngày phiếu thu' />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'></Label>
          <div className='w-1/2'>
            <FormField name='view_rs' type='checkbox' label='Xem chứng từ đã tạo' />
          </div>
        </div>
      </div>
    </div>
  );
};
