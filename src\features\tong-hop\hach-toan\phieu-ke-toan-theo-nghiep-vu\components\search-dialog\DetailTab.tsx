'use client';

import { QUERY_KEYS, accountSearchColumns, accountantSearchColumns, khachHangSearchColumns } from '@/constants';
import { AccountModel, DoiTuong } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface DetailTabProps {
  formMode: FormMode;
}

export const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mã khách hàng</Label>
          <SearchField<DoiTuong>
            type='text'
            name='customer_code'
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
            searchColumns={khachHangSearchColumns}
            disabled={formMode === 'view'}
            dialogTitle='Danh mục đối tượng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            classNameRelatedField='w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mã nghiệp vụ</Label>
          <SearchField<any>
            type='text'
            name='ma_ngvkt'
            searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_NGHIEP_VU_KE_TOAN}/`}
            searchColumns={accountantSearchColumns}
            disabled={formMode === 'view'}
            dialogTitle='Danh mục nghiệp vụ'
            columnDisplay='ma_ngvkt'
            displayRelatedField='ten_ngvkt'
            classNameRelatedField='w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tài khoản nợ</Label>
          <SearchField<AccountModel>
            type='text'
            name='code'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            disabled={formMode === 'view'}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            classNameRelatedField='w-full'
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tài khoản có</Label>
          <SearchField<AccountModel>
            type='text'
            name='code_co'
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
            searchColumns={accountSearchColumns}
            disabled={formMode === 'view'}
            dialogTitle='Danh mục tài khoản'
            columnDisplay='code'
            displayRelatedField='name'
            classNameRelatedField='w-full'
          />
        </div>
      </div>
    </div>
  );
};
