import { useState } from 'react';
import {
  QUERY_KEYS,
  accountSearchColumns,
  hanThanhToanSearchColumns,
  khachHangSearchColumns,
  nhanVienSearchColumns,
  MA_CHUNG_TU
} from '@/constants';
import { SearchField, FormField, AritoIcon, CurrencyInput, DocumentNumberField } from '@/components/custom/arito';
import { AccountModel, HanThanhToan, KhachHang, NhanVien } from '@/types/schemas';
import { FormFieldState, FormFieldActions } from '../../hooks';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

const tabsMini = [
  { id: 0, title: 'Chứng từ mua' },
  { id: 1, title: '<PERSON><PERSON><PERSON> đơn' },
  { id: 2, title: 'Phiếu nhập' }
];

export function BasicInfoTab({ formMode, formState: { state, actions } }: BasicInfoTabProps) {
  const isViewMode = formMode === 'view';
  const [selectedTabId, setSelectedTabId] = useState<number>(0);

  const pc_tao_yn = state.pc_tao_yn;
  const ck_yn = state.ck_yn;
  const loai_ck = state.loai_ck;

  return (
    <div className='flex items-center gap-2 p-2'>
      {/* Left Column */}
      <div className='basis-3/5'>
        <div className='flex items-center gap-6 pb-2'>
          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='hdbh_yn'
                id='hdbh_yn'
                defaultChecked={true || state.hdmh_yn}
                onCheckedChange={actions.setHdmhYn}
                disabled={formMode === 'view'}
                className='size-4 rounded-sm border-gray-300'
              />
              <Label className='ml-2'>Hóa đơn</Label>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='px_yn'
                id='px_yn'
                disabled={formMode === 'view'}
                defaultChecked={true || state.pn_yn}
                onCheckedChange={actions.setPnYn}
                className='size-4 rounded-sm border-gray-300'
              />
              <Label className='ml-2'>Nhập kho</Label>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='pc_tao_yn'
                id='pt_tpc_tao_yno_yn'
                disabled={formMode === 'view'}
                onCheckedChange={actions.setPcTaoYn}
                checked={pc_tao_yn}
                className='size-4 rounded-sm border-gray-300'
              />
              {!pc_tao_yn && <Label className='ml-2'>Chi tiền</Label>}
            </div>
            {pc_tao_yn && (
              <div className='flex items-center gap-2'>
                <FormField
                  name='ma_httt'
                  type='select'
                  disabled={formMode === 'view'}
                  className='w-[150px] flex-shrink-0'
                  options={[
                    { value: 'TMB', label: 'Tiền mặt' },
                    { value: 'CKB', label: 'Chuyển khoản' },
                    { value: 'KB', label: 'Khác' }
                  ]}
                  defaultValue={state.ma_httt}
                  onValueChange={actions.setMaHttt}
                />
              </div>
            )}
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <FormField
                name='xt_yn'
                type='checkbox'
                disabled={formMode === 'view'}
                defaultValue={state.xt_yn}
                onValueChange={actions.setXtYn}
              />
              <Label className='ml-2'>Xuất thẳng</Label>
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <div className='flex flex-shrink-0 items-center'>
              <Checkbox
                name='ck_yn'
                id='ck_yn'
                disabled={formMode === 'view'}
                checked={ck_yn}
                onCheckedChange={actions.setCkYn}
                className='size-4 rounded-sm border-gray-300'
              />
              {!ck_yn && <Label className='ml-2'>Chiết khấu</Label>}
            </div>
            {ck_yn && (
              <div className='flex items-center gap-2'>
                <FormField
                  name='loai_ck'
                  type='select'
                  disabled={formMode === 'view'}
                  className='w-[250px] flex-shrink-0'
                  options={[
                    { value: '1', label: 'Chiết khấu tự nhập chi tiết' },
                    { value: '2', label: 'Chiết khấu % tổng đôn' },
                    { value: '3', label: 'Giảm tiền trên tổng hóa đơn' }
                  ]}
                  defaultValue={'2'}
                  onValueChange={actions.setLoaiCk}
                />
                {loai_ck !== '1' && (
                  <FormField
                    name='ck_tl_nt'
                    type='number'
                    disabled={formMode === 'view'}
                    className='w-[150px] flex-shrink-0 pb-1'
                    defaultValue={0}
                  />
                )}
              </div>
            )}
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-[195px]'>Mã nhà cung cấp</Label>
          <SearchField<KhachHang>
            type='text'
            name='ma_kh'
            value={state.khachHang?.customer_number || ''}
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={khachHangSearchColumns}
            onRowSelection={actions.setKhachHang}
            columnDisplay='customer_code'
            dialogTitle='Danh mục đối tượng'
            disabled={isViewMode}
          />

          <div className='ml-4 flex items-center'>
            <Label className='mr-2'>Mã số thuế</Label>
            <FormField
              name='ma_so_thue'
              type='text'
              placeholder='Nhập và tra cứu'
              value={state.khachHang?.tax_code || ''}
              disabled={isViewMode}
            />
            <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
              <AritoIcon icon={15} className='shrink-0' />
            </button>
            <button className='ml-2 flex items-center justify-center border border-gray-200 px-3 py-1.5'>
              <AritoIcon icon={888} />
            </button>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-[195px]'>Tên nhà cung cấp</Label>
          <div className='w-[205px]'>
            <FormField name='ten_kh' type='text' value={state.khachHang?.customer_name || ''} disabled={isViewMode} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-[195px]'>Địa chỉ</Label>
          <div className='w-[205px]'>
            <FormField
              name='dia_chi'
              type='text'
              labelClassName='w-48'
              value={state.khachHang?.address || ''}
              disabled={isViewMode}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-[195px]'>Mã nhân viên</Label>
          <SearchField<NhanVien>
            type='text'
            name='ma_nvmh'
            value={state.nhanVien?.ma_nhan_vien || ''}
            relatedFieldValue={state.nhanVien?.ho_ten_nhan_vien || ''}
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
            columnDisplay='ma_nhan_vien'
            displayRelatedField='ho_ten_nhan_vien'
            searchColumns={nhanVienSearchColumns}
            dialogTitle='Danh sách nhân viên mua hàng'
            onRowSelection={actions.setNhanVien}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-[195px]'>Tài khoản có</Label>
          <SearchField<AccountModel>
            value={state.taiKhoan?.code || state.khachHang?.account_data?.code || ''}
            relatedFieldValue={state.taiKhoan?.name || state.khachHang?.account_data?.name || ''}
            searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
            columnDisplay='code'
            displayRelatedField='name'
            searchColumns={accountSearchColumns}
            dialogTitle='Danh sách tài khoản'
            onRowSelection={actions.setTaiKhoan}
            disabled={isViewMode}
          />
        </div>

        <div className='flex items-center'>
          <FormField
            label='Diễn giải'
            name='dien_giai'
            type='text'
            labelClassName='w-48'
            className='w-[600px]'
            disabled={isViewMode}
          />
        </div>
      </div>

      {/* Middle */}
      <div className='basis-2/5'>
        <div className='ml-4 flex items-center'>
          <FormField
            name='du_cn_thu'
            label='Dư công nợ'
            labelClassName='w-32'
            inputClassName='w-6 text-red-600'
            className='w-[200px]'
            disabled={true}
          />
        </div>

        <div className='ml-4 flex items-center'>
          <FormField
            name='ong_ba'
            label='Người giao hàng'
            labelClassName='w-32'
            inputClassName='w-6 text-red-600'
            className='w-[450px]'
            value={state.khachHang?.contact_person}
            disabled={isViewMode}
          />
        </div>

        <div className='ml-4 flex items-center'>
          <FormField
            name='e_mail'
            label='Email'
            labelClassName='w-32'
            inputClassName='w-64'
            className='w-[450px]'
            value={state.khachHang?.email}
            disabled={isViewMode}
          />
        </div>

        <div className='ml-4 flex items-center'>
          <div className='flex items-center'>
            <Label className='w-[132px]'>Hạn thanh toán</Label>
            <SearchField<HanThanhToan>
              type='text'
              name='ma_tt'
              value={state.hanThanhToan?.ma_tt || state.khachHang?.payment_term_data?.ma_tt || ''}
              searchEndpoint={`/${QUERY_KEYS.HAN_THANH_TOAN}/`}
              columnDisplay='ma_tt'
              searchColumns={hanThanhToanSearchColumns}
              dialogTitle='Danh mục hạn thanh toán'
              onRowSelection={actions.setHanThanhToan}
              className='w-[320px]'
              disabled={isViewMode}
            />
          </div>
        </div>
      </div>

      {/* Right Column */}
      <div className='mt-5 flex basis-1/5'>
        <div className='order-4 flex w-7 flex-col items-center gap-y-[4.2rem]'>
          {tabsMini
            .filter(tab => tab.id !== 2 || state.pn_yn)
            .map(tab => (
              <button
                key={tab.id}
                onClick={e => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSelectedTabId(tab.id);
                }}
                className={`h-[16px] w-[80px] rotate-90 rounded-sm text-center text-[10px] font-bold text-white ${
                  selectedTabId === tab.id ? 'bg-blue-600' : 'bg-gray-500 hover:bg-gray-600'
                }`}
              >
                {tab.title}
              </button>
            ))}
        </div>

        {/* Tab 0 */}
        {selectedTabId === 0 && (
          <div className='pr-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.MUA_HANG.HOA_DON_MUA_HANG_TRONG_NUOC}
              quyenChungTu={state.quyenChungTu}
              onQuyenChungTuChange={actions.setQuyenChungTu}
              soChungTu={state.soChungTu || ''}
              onSoChungTuChange={actions.setSoChungTu}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
            />

            <FormField
              label='Ngày chứng từ'
              name='ngay_ct'
              type='date'
              labelClassName='w-32 shrink-0'
              disabled={isViewMode}
            />

            <CurrencyInput formMode={formMode} labelClassName='w-[127px]' classNameInput='w-full' />

            <FormField
              name='status'
              type='select'
              label='Trạng thái'
              labelClassName='w-32 shrink-0'
              inputClassName='w-full'
              options={[
                { value: '0', label: 'Chưa ghi sổ' },
                { value: '3', label: 'Chờ duyệt' },
                { value: '5', label: 'Đã ghi sổ' }
              ]}
              disabled={isViewMode}
            />
            <div className='mt-1 flex'>
              <div className='mb-4 h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu được nhận'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='w-32'
                defaultValue={false}
              />
            </div>
          </div>
        )}

        {/* Tab 1 */}
        {selectedTabId === 1 && (
          <div className='pr-2'>
            <div className='flex items-center'>
              <Label className='w-32'>Số hóa đơn</Label>
              <FormField
                name='so_ct0'
                labelClassName='w-32 shrink-0'
                inputClassName='w-[205px]'
                className='w-[205px]'
                disabled={isViewMode}
              />
            </div>
            <FormField
              label='Ngày hóa đơn'
              name='ngay_ct0'
              type='date'
              labelClassName='w-32 shrink-0'
              disabled={isViewMode}
            />
            <FormField label='Ký hiệu' name='so_ct2' type='text' labelClassName='w-32 shrink-0' disabled={isViewMode} />

            <CurrencyInput formMode={formMode} labelClassName='w-[127px]' classNameInput='w-full' />

            <FormField
              name='status'
              type='select'
              label='Trạng thái'
              labelClassName='w-32 shrink-0'
              inputClassName='w-full'
              options={[
                { value: '0', label: 'Lập chứng từ' },
                { value: '3', label: 'Chờ duyệt' },
                { value: '5', label: 'Xuất hóa đơn' },
                { value: '7', label: 'Bỏ duyệt đơn hàng' }
              ]}
              disabled={isViewMode}
            />
            <div className='mt-1 flex'>
              <div className='h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu được nhận'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='w-32'
                defaultValue={false}
              />
            </div>
          </div>
        )}
        {/* Tab 2 */}
        {selectedTabId === 2 && state.pn_yn && (
          <div className='pr-2'>
            <DocumentNumberField
              ma_ct={MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO}
              quyenChungTu={state.quyenPhieuNhap}
              onQuyenChungTuChange={actions.setQuyenPhieuNhap}
              soChungTu={state.soPhieuNhap || ''}
              onSoChungTuChange={actions.setSoPhieuNhap}
              disabled={formMode === 'view'}
              classNameSearchField='w-full'
              label='Số ph/nhập'
            />

            <FormField
              label='Ngày nhập kho'
              name='ngay_pn'
              type='date'
              labelClassName='w-32 shrink-0'
              disabled={isViewMode}
            />

            <CurrencyInput formMode={formMode} labelClassName='w-[127px]' classNameInput='w-full' />

            <FormField
              name='status'
              type='select'
              label='Trạng thái'
              labelClassName='w-32 shrink-0'
              inputClassName='w-full'
              options={[
                { value: '0', label: 'Lập chứng từ' },
                { value: '3', label: 'Chờ duyệt' },
                { value: '5', label: 'Xuất hóa đơn' }
              ]}
              disabled={isViewMode}
            />
            <div className='mt-1 flex'>
              <div className='h-2 w-32 shrink-0' />
              <FormField
                label='Dữ liệu được nhận'
                name='transfer_yn'
                type='checkbox'
                disabled={true}
                labelClassName='w-32'
                defaultValue={false}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
