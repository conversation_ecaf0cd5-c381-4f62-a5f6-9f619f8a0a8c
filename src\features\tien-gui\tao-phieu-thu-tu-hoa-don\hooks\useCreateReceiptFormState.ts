import { useState } from 'react';
import { <PERSON>Tu, NgoaiTe, QuyenChungTu, TaiKhoanNganHang, AccountModel } from '@/types/schemas';

export interface CreateReceiptFormState {
  chungTu: ChungTu | null;
  ngoaiTe: NgoaiTe | null;
  quyenChungTu: QuyenChungTu | null;
  taiKhoanNganHang: TaiKhoanNganHang | null;
  taiKhoan: AccountModel | null;
}

export interface CreateReceiptFormActions {
  setChungTu: (chungTu: ChungTu | null) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe | null) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => void;
  setTaiKhoanNganHang: (taiKhoanNganHang: TaiKhoanNganHang | null) => void;
  setTaiKhoan: (taiKhoanNo: AccountModel | null) => void;
  reset: () => void;
}

export interface UseCreateReceiptFormStateReturn {
  state: CreateReceiptFormState;
  actions: CreateReceiptFormActions;
}

const initialState: CreateReceiptFormState = {
  chungTu: null,
  ngoaiTe: null,
  quyenChungTu: null,
  taiKhoanNganHang: null,
  taiKhoan: null
};

export const useCreateReceiptFormState = (): UseCreateReceiptFormStateReturn => {
  const [state, setState] = useState<CreateReceiptFormState>(initialState);

  const actions: CreateReceiptFormActions = {
    setChungTu: (chungTu: ChungTu | null) => {
      setState(prev => ({ ...prev, chungTu }));
    },
    setNgoaiTe: (ngoaiTe: NgoaiTe | null) => {
      setState(prev => ({ ...prev, ngoaiTe }));
    },
    setQuyenChungTu: (quyenChungTu: QuyenChungTu | null) => {
      setState(prev => ({ ...prev, quyenChungTu }));
    },
    reset: () => {
      setState(initialState);
    },
    setTaiKhoanNganHang: (taiKhoanNganHang: TaiKhoanNganHang | null) => {
      setState(prev => ({ ...prev, taiKhoanNganHang }));
    },
    setTaiKhoan: (taiKhoan: AccountModel | null) => {
      setState(prev => ({ ...prev, taiKhoan }));
    }
  };

  return {
    state,
    actions
  };
};
