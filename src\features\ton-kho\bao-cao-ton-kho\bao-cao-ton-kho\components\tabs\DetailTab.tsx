import React, { useState } from 'react';
import { QUERY_KEYS, loSearchColumns, nhomColumns, vatTuSearchColumns, viTriSearchColumns } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito';
import SaveTemplateDialog from '../SaveTemplateDialog';
import { useLoaiVatTu } from '@/hooks/queries';
import { Label } from '@/components/ui/label';

interface Props {
  searchFieldStates?: any;
}

const DetailTab: React.FC<Props> = ({ searchFieldStates }) => {
  const [saveTemplateDialogOpen, setSaveTemplateDialogOpen] = useState(false);
  const { loaiVatTus } = useLoaiVatTu();

  const loaiVatTuOptions = [
    ...loaiVatTus.map(item => ({
      value: item.uuid,
      label: `${item.ma_lvt} - ${item.ten_lvt}`
    }))
  ];

  const handleSaveTemplateClose = () => {
    setSaveTemplateDialogOpen(false);
  };

  const handleSaveTemplate = (data: { ten_bao_cao: string; ten_khac?: string }) => {
    console.log('Saving new template:', data);
    setSaveTemplateDialogOpen(false);
  };

  return (
    <div className='min-h-[320px] p-6'>
      <div className='grid grid-cols-1 gap-x-6 gap-y-2'>
        {/* Mã vật tư field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Mã vật tư:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              dialogTitle='Danh mục vật tư'
              columnDisplay='ma_vt'
              displayRelatedField='ten_vt'
              value={searchFieldStates?.material?.ma_vt || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setMaterial(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã lô field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Mã lô:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={loSearchColumns}
              dialogTitle='Danh mục lô'
              columnDisplay='ma_lo'
              displayRelatedField='ten_lo'
              value={searchFieldStates?.lot?.ma_lo || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setLot(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vị trí field */}
        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Mã vị trí:</Label>
          <div className='ml-2 w-[69%]'>
            <SearchField
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
              searchColumns={viTriSearchColumns}
              dialogTitle='Danh mục vị trí kho hàng'
              columnDisplay='ma_vi_tri'
              displayRelatedField='ten_vi_tri'
              value={searchFieldStates?.location?.ma_vi_tri || ''}
              onRowSelection={(row: any) => {
                searchFieldStates?.setLocation(row);
              }}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Loại vật tư field */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-left'>Loại vật tư:</Label>
          <div className='ml-2 flex w-full items-center gap-4'>
            <div className='flex-1'>
              <FormField name='ma_lvt' type='select' options={loaiVatTuOptions} />
            </div>
            <div className='whitespace-nowrap'>
              <FormField name='ton_kho_yn' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='min-w-32 text-left'>Nhóm vật tư:</Label>
          <div className='ml-2 flex-1'>
            <div className='grid grid-cols-3 gap-2'>
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT1`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư'
                columnDisplay='ma_nhom'
                value={searchFieldStates?.materialGroup1?.ma_nhom || ''}
                onRowSelection={(row: any) => {
                  searchFieldStates?.setMaterialGroup1(row);
                }}
                className='w-full'
                classNameRelatedField='w-auto'
              />
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT2`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư'
                columnDisplay='ma_nhom'
                value={searchFieldStates?.materialGroup2?.ma_nhom || ''}
                onRowSelection={(row: any) => {
                  searchFieldStates?.setMaterialGroup2(row);
                }}
                className='w-full'
                classNameRelatedField='w-auto'
              />
              <SearchField
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM}/?loai_nhom=VT3`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư'
                columnDisplay='ma_nhom'
                value={searchFieldStates?.materialGroup3?.ma_nhom || ''}
                onRowSelection={(row: any) => {
                  searchFieldStates?.setMaterialGroup3(row);
                }}
                className='w-full'
                classNameRelatedField='w-auto'
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Nhóm theo</label>
          <div className='flex w-full items-center gap-2'>
            <div className='w-2/3'>
              <FormField
                name='group_by'
                type='select'
                className='w-full'
                options={[
                  { value: '', label: 'Không phân nhóm' },
                  { value: 'loai_vat_tu', label: 'Loại vật tư' },
                  { value: 'nhom_vat_tu_1', label: 'Nhóm vật tư 1' },
                  { value: 'nhom_vat_tu_2', label: 'Nhóm vật tư 2' },
                  { value: 'nhom_vat_tu_3', label: 'Nhóm vật tư 3' }
                ]}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <label className='min-w-32 text-left text-sm font-medium text-gray-700'>Mẫu báo cáo</label>
          <div className='flex w-full items-center gap-2'>
            <div className='w-2/3'>
              <FormField
                name='mau_bc'
                type='select'
                className='w-full'
                options={[
                  { value: 20, label: 'Mẫu số lượng' },
                  { value: 21, label: 'Mẫu số lượng và giá trị' },
                  { value: 22, label: 'Mẫu số lượng và giá trị ngoại tệ' }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Template Dialog */}
      <SaveTemplateDialog open={saveTemplateDialogOpen} onClose={handleSaveTemplateClose} onSave={handleSaveTemplate} />
    </div>
  );
};

export default DetailTab;
