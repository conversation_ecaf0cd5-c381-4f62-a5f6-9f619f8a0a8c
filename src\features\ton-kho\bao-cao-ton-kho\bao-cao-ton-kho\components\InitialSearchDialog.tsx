import { Button } from '@mui/material';
import React from 'react';
import { AritoForm, AritoIcon, AritoHeaderTabs, AritoDialog } from '@/components/custom/arito';
import { searchSchema, initialValues, SearchFormValues } from '../schemas';
import { useSearchFieldStates, useBaoCaoTonKho } from '../hooks';
import { BasicInfo, DetailTab, OtherTab } from './tabs';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => void;
}

const InitialSearchDialog = ({ open, onClose, onSearch }: InitialSearchDialogProps) => {
  const searchFieldStates = useSearchFieldStates();
  const { submitBaoCaoTonKho } = useBaoCaoTonKho({});

  const handleSubmit = async (data: SearchFormValues) => {
    const searchFieldData = searchFieldStates.getSearchFieldData();

    const combinedData = {
      ...data,
      ...searchFieldData,
      ma_lvt: data.ma_lvt || null
    };

    try {
      await submitBaoCaoTonKho(combinedData);
      onSearch(combinedData);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo tồn kho'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo searchFieldStates={searchFieldStates} />
            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailTab searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'others',
                  label: 'Khác',
                  component: <OtherTab searchFieldStates={searchFieldStates} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 px-2 py-1'
        bottomBar={
          <>
            <Button
              className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
              variant='contained'
            >
              <AritoIcon icon={884} marginX='4px' />
              Đồng ý
            </Button>

            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} marginX='4px' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
