import { useState } from 'react';
import {
  Account<PERSON><PERSON><PERSON>,
  Quyen<PERSON>hung<PERSON>u,
  Han<PERSON><PERSON>h<PERSON><PERSON>,
  KhachHang,
  Nhan<PERSON><PERSON>,
  <PERSON><PERSON>i<PERSON><PERSON>,
  DonViCoSo,
  DiaChi,
  PhuongTienVan<PERSON>huyen,
  PhuongThucGiaoHang
} from '@/types/schemas';

export interface FormFieldState {
  khachHang: KhachHang | null;
  nhanVien: NhanVien | null;
  taiKhoan: AccountModel | null;
  hanThanhToan: HanThanhToan | null;

  quyenChungTu: QuyenChungTu | null;
  quyenPhieuXuat: QuyenChungTu | null;
  ngoaiTe: NgoaiTe | null;
  donViCoSo: DonViCoSo | null;

  diaChi: DiaChi | null;
  phuongTienVanChuyen: PhuongTienVanChuyen | null;
  phuongTienGiaoHang: PhuongThucGiaoHang | null;
  cucThue: KhachHang | null;

  soChungTu: string;
  soPhieuXuat: string;

  hdbh_yn: boolean;
  px_yn: boolean;
  pt_tao_yn: boolean;
  ck_yn: boolean;
  loai_ck: string;

  ma_httt: string;
}

export interface FormFieldActions {
  setKhachHang: (khachHang: KhachHang) => void;
  setNhanVien: (nhanVien: NhanVien) => void;
  setTaiKhoan: (taiKhoan: AccountModel) => void;
  setHanThanhToan: (hanThanhToan: HanThanhToan) => void;
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;
  setQuyenPhieuXuat: (quyenPhieuXuat: QuyenChungTu) => void;
  setNgoaiTe: (ngoaiTe: NgoaiTe) => void;
  setDonViCoSo: (donViCoSo: DonViCoSo) => void;

  setDiaChi: (diaChi: DiaChi) => void;
  setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => void;
  setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => void;
  setCucThue: (cucThue: KhachHang) => void;

  setSoChungTu: (soChungTu: string) => void;
  setSoPhieuXuat: (soPhieuXuat: string) => void;

  setHdbhYn: (value: boolean) => void;
  setPxYn: (value: boolean) => void;
  setPtTaoYn: (value: boolean) => void;
  setCkYn: (value: boolean) => void;
  setLoaiCk: (value: string) => void;

  setMaHttt: (value: string) => void;

  resetState: () => void;
  updateState: (updates: Partial<FormFieldState>) => void;
}

export interface UseFormFieldStateReturn {
  state: FormFieldState;
  actions: FormFieldActions;
}

const initialState: FormFieldState = {
  khachHang: null,
  nhanVien: null,
  taiKhoan: null,
  hanThanhToan: null,

  quyenChungTu: null,
  quyenPhieuXuat: null,
  ngoaiTe: null,
  donViCoSo: null,

  diaChi: null,
  phuongTienVanChuyen: null,
  phuongTienGiaoHang: null,
  cucThue: null,

  soChungTu: '',
  soPhieuXuat: '',

  hdbh_yn: true,
  px_yn: true,
  pt_tao_yn: false,
  ck_yn: false,
  loai_ck: '2',

  ma_httt: 'TMB'
};

function transformInitialData(initialData?: any): Partial<FormFieldState> {
  if (!initialData) return {};

  return {
    khachHang: initialData.ma_kh_data || null,
    nhanVien: initialData.ma_nvbh_data || null,
    taiKhoan: initialData.tk_data || null,
    hanThanhToan: initialData.ma_tt_data || null,

    quyenChungTu: initialData.ma_nk_data || null,
    quyenPhieuXuat: initialData.ma_nk_px_data || null,
    ngoaiTe: initialData.ma_nt_data || null,
    donViCoSo: initialData.unit_id_data || null,

    diaChi: initialData.ma_dc_data || null,
    phuongTienVanChuyen: initialData.ma_ptvc_data || null,
    phuongTienGiaoHang: initialData.ma_ptgh_data || null,
    cucThue: initialData.ma_kh9_data || null,

    soChungTu: initialData.so_ct || '',
    soPhieuXuat: initialData.so_px || '',

    hdbh_yn: initialData.hdbh_yn ?? true,
    px_yn: initialData.px_yn ?? true,
    pt_tao_yn: initialData.pt_tao_yn ?? false,
    ck_yn: initialData.ck_yn ?? false,
    loai_ck: initialData.loai_ck || '2',

    ma_httt: initialData.ma_httt || 'TMB'
  };
}

export function useFormFieldState(initialData?: any): UseFormFieldStateReturn {
  const [state, setState] = useState<FormFieldState>({
    ...initialState,
    ...transformInitialData(initialData)
  });

  const actions: FormFieldActions = {
    // Search field setters
    setKhachHang: (khachHang: KhachHang) => {
      setState(prev => ({
        ...prev,
        khachHang
      }));
    },

    setNhanVien: (nhanVien: NhanVien) => {
      setState(prev => ({
        ...prev,
        nhanVien
      }));
    },

    setTaiKhoan: (taiKhoan: AccountModel) => {
      setState(prev => ({
        ...prev,
        taiKhoan
      }));
    },

    setHanThanhToan: (hanThanhToan: HanThanhToan) => {
      setState(prev => ({
        ...prev,
        hanThanhToan
      }));
    },

    setQuyenChungTu: (quyenChungTu: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenChungTu
      }));
    },

    setQuyenPhieuXuat: (quyenPhieuXuat: QuyenChungTu) => {
      setState(prev => ({
        ...prev,
        quyenPhieuXuat
      }));
    },

    setNgoaiTe: (ngoaiTe: NgoaiTe) => {
      setState(prev => ({
        ...prev,
        ngoaiTe
      }));
    },

    setDonViCoSo: (donViCoSo: DonViCoSo) => {
      setState(prev => ({
        ...prev,
        donViCoSo
      }));
    },

    setDiaChi: (diaChi: DiaChi) => {
      setState(prev => ({
        ...prev,
        diaChi
      }));
    },

    setPhuongTienVanChuyen: (phuongTienVanChuyen: PhuongTienVanChuyen) => {
      setState(prev => ({
        ...prev,
        phuongTienVanChuyen
      }));
    },

    setPhuongTienGiaoHang: (phuongTienGiaoHang: PhuongThucGiaoHang) => {
      setState(prev => ({
        ...prev,
        phuongTienGiaoHang
      }));
    },

    setCucThue: (cucThue: KhachHang) => {
      setState(prev => ({
        ...prev,
        cucThue
      }));
    },

    // So chung tu
    setSoChungTu: (soChungTu: string) => {
      setState(prev => ({ ...prev, soChungTu }));
    },

    setSoPhieuXuat: (soPhieuXuat: string) => {
      setState(prev => ({ ...prev, soPhieuXuat }));
    },

    // Checkbox setters
    setHdbhYn: (value: boolean) => {
      setState(prev => ({ ...prev, hdbh_yn: value }));
    },

    setPxYn: (value: boolean) => {
      setState(prev => ({ ...prev, px_yn: value }));
    },

    setPtTaoYn: (value: boolean) => {
      setState(prev => ({ ...prev, pt_tao_yn: value }));
    },

    setCkYn: (value: boolean) => {
      setState(prev => ({ ...prev, ck_yn: value }));
    },

    setLoaiCk: (value: string) => {
      setState(prev => ({ ...prev, loai_ck: value }));
    },

    // Payment method setter
    setMaHttt: (value: string) => {
      setState(prev => ({ ...prev, ma_httt: value }));
    },

    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<FormFieldState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
