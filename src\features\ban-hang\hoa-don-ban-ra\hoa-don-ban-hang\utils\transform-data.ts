import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) && !row.uuid.startsWith('temp-') ? row.uuid : null,
    ma_vt: row.ma_vt_data?.uuid || '',
    dvt: row.ma_vt_data?.dvt || '',
    ma_kho: row.ma_kho_data?.uuid || '',
    ct_km: row.ct_km || '0',
    so_luong: row.so_luong,
    gia_nt2: row.gia_nt2,
    tien_nt2: row.tien_nt2,
    px_dd: row.px_dd,
    gia_nt: row.gia_nt,
    tien_nt: row.tien_nt,
    ma_thue: row.ma_thue_data?.uuid || '',
    thue_nt: row.thue_nt,
    thanh_tien: row.thanh_tien,
    don_gia: row.don_gia,
    giam_gia: row.giam_gia,
    thue_suat: row.thue_suat,
    tk_thue_co: row.tk_thue_co_data?.uuid || '',
    tk_dt: row.tk_dt_data?.uuid || '',
    tk_gv: row.tk_gv_data?.uuid || '',
    tk_vt: row.tk_vt_data?.uuid || '',
    tk_ck: row.tk_ck_data?.uuid || '',
    ghi_chu: row.ghi_chu || '',
    ma_bp: row.ma_bp_data?.uuid || '',
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null,
    sl_px: row.sl_px || null,
    line_dh: row.line_dh || null,
    line_hd: row.line_hd || null
  }));
};

/**
 * Transform account rows for API submission
 * @param accountRows - Array of account row data from the form
 * @returns Transformed account rows ready for API submission
 */
export const transformAccountRows = (accountRows: any[]) => {
  return accountRows.map((row: any, index: number) => ({
    line: index + 1,
    uuid: isValidUUID(row.uuid) && !row.uuid.startsWith('temp-') ? row.uuid : null,
    ma_httt: row.ma_httt_data?.uuid || '',
    ten_httt: row.ma_httt_data?.ten_httt || '',
    tknh: row.tknh || '',
    tk: row.tk_data?.uuid || '',
    ma_ct: row.ma_ct_data?.uuid || '',
    ngay_ct: row.ngay_ct || '',
    ma_nk: row.ma_nk_data?.uuid || '',
    t_tt_nt: row.t_tt_nt || 0,
    id_ct_tt: row.ma_ct_data?.uuid || ''
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param accountRows - Array of account row data
 * @param totals - Calculated totals from calculateTotals function
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  detailRows: any[] = [],
  accountRows: any[] = [],
  totals?: any
) => {
  const detail = transformDetailRows(detailRows);
  const account = transformAccountRows(accountRows);

  const hasDetailRows = detailRows && detailRows.length > 0;

  const {
    tong_sl = 0,
    tong_tien = 0,
    tong_thue = 0,
    tong_tien_von = 0,
    tong_thanh_toan = 0,
    tong_ck = 0
  } = totals || {};

  const formData: any = {
    ...data,
    ngay_bk: data.ngay_bk || null,

    hdbh_yn: state.hdbh_yn,
    px_yn: state.px_yn,
    pt_tao_yn: state.pt_tao_yn,
    ck_yn: state.ck_yn,
    loai_ck: state.loai_ck,

    ma_httt: state.ma_httt || 'TMB',

    ma_kh: state.khachHang?.uuid || '',
    ma_so_thue: data.ma_so_thue || state.khachHang?.tax_code || '',
    ten_kh_thue: data.ten_kh_thue || state.khachHang?.customer_name || '',
    dia_chi: data.dia_chi || state.khachHang?.address || '',
    ong_ba: data.ong_ba || state.khachHang?.contact_person || '',
    e_mail: data.e_mail || state.khachHang?.email || '',
    dien_giai: data.dien_giai || state.khachHang?.description || '',

    ma_nvbh: state.nhanVien?.uuid || state.khachHang?.sales_rep_data?.uuid || '',
    tk: state.taiKhoan?.uuid || state.khachHang?.account_data?.uuid || '',
    ma_tt: state.hanThanhToan?.uuid || state.khachHang?.payment_term_data?.uuid || '',
    ma_dc: state.diaChi?.uuid || '',
    ma_ptvc: state.phuongTienVanChuyen?.uuid || '',
    ma_ptgh: state.phuongTienGiaoHang?.uuid || '',
    ma_kh9: state.cucThue?.uuid || '',

    ma_ngv: '1',
    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.BAN_HANG.HOA_DON_BAN_HANG),
    i_so_px: transformDocumentNumber(state.quyenPhieuXuat, state.soPhieuXuat || '', MA_CHUNG_TU.TON_KHO.PHIEU_XUAT_KHO)
      .i_so_ct,
    ma_nk_px: transformDocumentNumber(state.quyenPhieuXuat, state.soPhieuXuat || '', MA_CHUNG_TU.TON_KHO.PHIEU_XUAT_KHO)
      .ma_nk,
    so_px: transformDocumentNumber(state.quyenPhieuXuat, state.soPhieuXuat || '', MA_CHUNG_TU.TON_KHO.PHIEU_XUAT_KHO)
      .so_ct,
    so_ct2: data.so_ct2,
    so_ct0: data.so_ct0,
    ngay_ct: data.ngay_ct || '',
    ngay_lct: data.ngay_ct || '',

    // E-invoice information
    so_ct_hddt: data.so_ct_hddt || '',
    ngay_ct_hddt: data.ngay_ct_hddt || '',
    so_ct2_hddt: data.so_ct2_hddt || '',

    // Calculated totals from detailRows
    t_sl: tong_sl,
    t_tien_nt2: tong_tien,
    t_tien2: tong_tien,
    t_thue_nt: tong_thue,
    t_thue: tong_thue,
    t_ck_nt: tong_ck,
    t_ck: tong_ck,
    t_tt_nt: tong_thanh_toan,
    t_tt: tong_thanh_toan,
    t_tien_von: tong_tien_von,

    // Always include payment information
    thong_tin_thanh_toan: account
  };

  // Only include chi_tiet if user has created detail rows
  if (hasDetailRows) {
    formData.chi_tiet = detail;
  }

  return formData;
};
