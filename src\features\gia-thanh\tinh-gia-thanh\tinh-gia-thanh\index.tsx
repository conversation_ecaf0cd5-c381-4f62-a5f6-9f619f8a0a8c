'use client';

import { useState } from 'react';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { ActionBar, SearchDialog, ResultDialog } from './components';
import { GiaThanhCacBuocSearchParams } from '@/types/schemas';
import { CostCalculationColumns } from './cols-definition';
import { SearchFormValues } from './schema';
import { useCRUD, useRows } from '@/hooks';
import { QUERY_KEYS } from '@/constants';
import { useDialogState } from './hooks';

export default function TinhGiaThanh() {
  const {
    data: giaThanhCacBuocs,
    addItem: addGiaThanhCacBuoc,
    isLoading
  } = useCRUD<any, GiaThanhCacBuocSearchParams>({
    endpoint: QUERY_KEYS.GIA_THANH_CAC_BUOC
  });
  const [searchParams, setSearchParams] = useState<SearchFormValues>();
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows();
  const {
    showData,
    showSearchDialog,
    showResultDialog,

    openSearchDialog,
    closeSearchDialog,
    openResultDialog,
    closeResultDialog,

    handleSearchButtonClick
  } = useDialogState(clearSelection);

  const tables = [
    {
      name: '',
      rows: giaThanhCacBuocs,
      columns: CostCalculationColumns(openResultDialog)
    }
  ];

  const handleSearch = async (data: any) => {
    setSearchParams(data);
    await addGiaThanhCacBuoc({ ky: data.period, nam: data.year, show_all: false });
    handleSearchButtonClick();
  };

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          openSearchDialog={showSearchDialog}
          onCloseSearchDialog={closeSearchDialog}
          onSearch={handleSearch}
        />
      )}

      {showResultDialog && (
        <ResultDialog
          openResultDialog={showResultDialog}
          onCloseResultDialog={closeResultDialog}
          selectedObj={giaThanhCacBuocs}
        />
      )}

      {showData && (
        <>
          <ActionBar isViewDisabled={!selectedObj} onSearchClick={openSearchDialog} searchParams={searchParams} />
          {isLoading && <LoadingOverlay />}
          <div className='w-full overflow-hidden'>
            {!isLoading && (
              <AritoDataTables
                tables={tables}
                selectedRowId={selectedRowIndex || undefined}
                onRowClick={handleRowClick}
              />
            )}
          </div>
        </>
      )}
    </div>
  );
}
