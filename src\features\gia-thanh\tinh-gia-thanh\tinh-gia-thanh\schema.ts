import { z } from 'zod';

export const SearchFormSchema = z.object({
  period: z.coerce.number().min(1, { message: '<PERSON><PERSON> phải lớn hơn 0' }),
  year: z.coerce.number().min(2000, { message: 'Năm phải lớn hơn 2000' }),
  unit: z.string().nonempty()
});
export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  period: new Date().getMonth() + 1,
  year: new Date().getFullYear(),
  unit: ''
};
