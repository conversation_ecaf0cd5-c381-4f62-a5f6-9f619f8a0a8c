import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';
import { FormMode } from '@/types/form';

/**
 * Transform detail rows for PhieuKeToan API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[], formMode: FormMode) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),

    tk: row.tk_data?.uuid || '',

    ma_kh: row.ma_kh_data?.uuid || '',

    ty_gia2: row.ty_gia2 || 1,
    ps_no_nt: row.ps_no_nt || 0,
    ps_co_nt: row.ps_co_nt || 0,
    ps_no: row.ps_co_nt || 0,
    ps_co: row.ps_no_nt || 0,

    nh_dk: row.nh_dk || '',
    dien_giai: row.dien_giai || '',
    so_ct0: row.so_ct0 || '',
    ngay_ct0: row.ngay_ct0 ? format(new Date(row.ngay_ct0), 'yyyy-MM-dd') : null,

    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_lsx: row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || null,

    id_tt: row.id_tt || 0,
    split_thue: row.split_thue || false
  }));
};

export const transformTaxRows = (taxRows: any[], formMode: FormMode) => {
  return taxRows.map((row: any, index: number) => ({
    line: index + 1,
    ...(isValidUUID(row.uuid) && formMode !== 'add' && { uuid: row.uuid }),

    so_ct0: row.so_ct0,
    so_ct2: row.so_ct2,
    ngay_ct0: row.ngay_ct0,

    ma_thue: row.ma_thue_data?.uuid || '',
    ma_mau_ct: row.ma_mau_ct_data?.uuid,
    ma_mau_bc: row.ma_mau_bc,
    ma_tc_thue: row.ma_tc_thue_data?.uuid,

    tk_thue_no: row.tk_thue_no_data?.uuid || '',
    tk_du: row.tk_du_data?.uuid || '',

    ma_kh: row.ma_kh_data?.uuid || null,
    ma_so_thue: row.ma_kh_data?.tax_code || row.ma_so_thue || '',
    dia_chi: row.ma_kh_data?.address,
    ten_vt_thue: row.ten_vt_thue,
    t_tien_nt: row.t_tien_nt,
    t_thue_nt: row.t_thue_nt,

    ma_tt: row.ma_tt_data?.uuid || null,

    ma_bp: row.ma_bp_data?.uuid || null,
    ma_vv: row.ma_vv_data?.uuid || null,
    ma_hd: row.ma_hd_data?.uuid || null,
    ma_dtt: row.ma_dtt_data?.uuid || null,
    ma_ku: row.ma_ku_data?.uuid || null,
    ma_phi: row.ma_phi_data?.uuid || null,
    ma_sp: row.ma_sp_data?.uuid || null,
    ma_cp0: row.ma_cp0_data?.uuid || null,
    ma_kh9: row.ma_kh9_data?.uuid || null,
    ma_lsx: '',

    ghi_chu: row.ghi_chu || ''
  }));
};

/**
 * Transform form data for PhieuKeToan API submission
 * @param data - Form data from the form
 * @param state - Form field state containing references to selected entities
 * @param entityUnit - Current entity unit
 * @param detailRows - Array of detail row data
 * @param taxRows - Array of tax row data
 * @param totals - Calculated totals
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  entityUnit: any,
  detailRows: any[],
  taxRows: any[],
  totals: any,
  formMode: FormMode
) => {
  const detail = transformDetailRows(detailRows, formMode);
  const tax = transformTaxRows(taxRows, formMode);

  return {
    unit_id: entityUnit?.uuid || '',

    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu || '', MA_CHUNG_TU.TONG_HOP.PHIEU_KE_TOAN),

    ma_nt: data.ma_nt,
    ty_gia: data.ty_gia,

    ngay_ct: data.ngay_ct || '',
    dien_giai: data.dien_giai || '',

    transfer_yn: data.transfer_yn || false,

    status: data.status,

    t_ps_no_nt: totals.t_ps_no_nt || 0,
    t_ps_co_nt: totals.t_ps_co_nt || 0,
    t_ps_no: totals.t_ps_no || totals.t_ps_no_nt || 0,
    t_ps_co: totals.t_ps_co || totals.t_ps_co_nt || 0,

    chi_tiet: detail,
    thue: tax
  };
};
