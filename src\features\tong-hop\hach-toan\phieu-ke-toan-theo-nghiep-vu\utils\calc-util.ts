export interface CalculationResult {
  totalAmount: number;
  totalAmountNT: number;
}

/**
 * Calculate totals from detail rows for <PERSON>eu <PERSON>
 * @param detailItems - Array of detail rows
 * @param exchangeRate - Exchange rate to calculate ps from ps_nt (default: 1)
 * @returns Calculation result with totals
 */
export function calculateTotals(detailItems: any[] = [], exchangeRate: number = 1): CalculationResult {
  // Calculate total amount in VND (ps_nt field)
  const totalAmountNT = detailItems.reduce((sum, item) => sum + (Number(item.ps_nt) || 0), 0);

  // Calculate total amount in base currency (ps field)
  // If ps field exists, use it; otherwise calculate from ps_nt * exchangeRate
  const totalAmount = detailItems.reduce((sum, item) => {
    const ps = Number(item.ps) || 0;
    const ps_nt = Number(item.ps_nt) || 0;

    // If ps exists and is not 0, use it; otherwise calculate from ps_nt
    if (ps !== 0) {
      return sum + ps;
    } else {
      return sum + ps_nt * exchangeRate;
    }
  }, 0);

  return {
    totalAmount: roundToDecimals(totalAmount),
    totalAmountNT: roundToDecimals(totalAmountNT)
  };
}

/**
 * Round number to specified decimal places
 * @param value - Number to round
 * @param decimals - Number of decimal places (default: 2)
 * @returns Rounded number
 */
export function roundToDecimals(value: number, decimals: number = 2): number {
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
}

/**
 * Format number for display
 * @param value - Number to format
 * @returns Formatted string
 */
export function formatCalculatedValue(value: number): string {
  return value.toFixed(2);
}

export const calculatePs = (ps_nt: number, ty_gia: number) => {
  return (ps_nt || 0) * (ty_gia || 1);
};
