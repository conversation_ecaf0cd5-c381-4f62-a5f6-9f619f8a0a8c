import { useState } from 'react';

interface UseDialogStateReturn {
  showData: boolean;
  showSearchDialog: boolean;
  showResultDialog: boolean;

  openSearchDialog: () => void;
  closeSearchDialog: () => void;

  openResultDialog: () => void;
  closeResultDialog: () => void;

  handleSearchButtonClick: () => void;
}

const useDialogState = (clearSelection?: () => void): UseDialogStateReturn => {
  const [showData, setShowData] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showResultDialog, setShowResultDialog] = useState(false);

  const openSearchDialog = () => setShowSearchDialog(true);
  const closeSearchDialog = () => setShowSearchDialog(false);

  const openResultDialog = () => setShowResultDialog(true);
  const closeResultDialog = () => setShowResultDialog(false);

  const handleSearchButtonClick = () => {
    if (clearSelection) clearSelection();
    setShowData(true);
    closeSearchDialog();
  };

  return {
    showData,
    showSearchDialog,
    showResultDialog,

    openSearchDialog,
    closeSearchDialog,

    openResultDialog,
    closeResultDialog,

    handleSearchButtonClick
  };
};
export default useDialogState;
